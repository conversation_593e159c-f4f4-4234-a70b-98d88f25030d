# SimplerEnv 统一演示系统

这个项目提供了一个重新组织和清理后的SimplerEnv演示系统，将所有核心功能整合到一个统一的脚本中，便于使用和维护。

## 🎯 项目概述

本项目是SimplerEnv多样化场景系统的重新组织版本，提供了：

- **统一的演示脚本**: 所有功能集成在单一脚本中
- **清晰的代码结构**: 移除冗余文件，保留核心功能
- **标准化输出**: 所有输出统一保存到指定目录
- **模块化设计**: 易于扩展和维护

## 🚀 核心功能

### 1. 多样化场景演示
- **基础多样化场景**: 随机物体数量和位置的基本场景
- **增强多样化场景**: 包含光照、材质、物理属性变化的高级场景  
- **杂乱多样化场景**: 高密度物体分布的复杂场景
- **场景多样性分析**: 统计分析场景变化情况

### 2. 视频录制功能
- **环境交互录制**: 录制机器人与环境的交互过程
- **多格式输出**: 支持视频(MP4)和图像(PNG)输出
- **详细报告**: 生成包含录制信息的JSON报告

### 3. 图像捕获系统
- **多相机支持**: 支持多种相机视角
- **自动分类保存**: 按场景类型自动分类保存图像
- **高质量渲染**: 支持分割掩码和高分辨率输出

## 📁 重新组织后的文件结构

```
Z1_new_trial/
├── simpler_env_demo.py           # 🌟 统一演示脚本 (主要文件)
├── SimplerEnv/                   # SimplerEnv核心实现
│   ├── ManiSkill2_real2sim/      # 环境实现
│   ├── custom_scene_config.py   # 场景配置系统
│   ├── diversity_enhancer.py    # 多样性增强器
│   └── ...                      # 其他核心文件
├── README.md                     # 📖 使用文档 (本文件)
├── IMPLEMENTATION_SUMMARY.md     # 实现总结
├── README_CUSTOM_SCENES.md       # 自定义场景文档
├── RECORDING_GUIDE.md            # 录制指南
├── SUMMARY.md                    # 项目总结
└── USAGE_EXAMPLES.md             # 使用示例
```

### 输出目录结构

所有输出统一保存到 `/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/`:

```
Z_new_trial/
├── images/                       # 图像输出
│   ├── basic_scenes/            # 基础场景图像
│   ├── enhanced_scenes/         # 增强场景图像
│   ├── cluttered_scenes/        # 杂乱场景图像
│   └── [env_name]/              # 特定环境图像
├── videos/                       # 视频输出
│   └── [env_name]/              # 按环境分类的视频
└── reports/                      # 报告文件
    ├── demo_report_*.json       # 演示报告
    └── recording_report_*.json  # 录制报告
```

## 🛠️ 安装和设置

### 前置条件

1. Python 3.8+
2. SimplerEnv 基础环境已安装
3. ManiSkill2_real2sim 已配置

### 环境变量设置

```bash
export MUJOCO_GL=egl
export CUDA_VISIBLE_DEVICES=0
export MS2_REAL2SIM_ASSET_DIR=/path/to/SimplerEnv/ManiSkill2_real2sim/data
```

## 🎮 使用方法

### 统一演示脚本

新的 `simpler_env_demo.py` 是主要的演示脚本，提供三种运行模式：

#### 1. 演示模式 (推荐)
```bash
python simpler_env_demo.py --mode demo
```
运行所有核心演示功能：
- 基础多样化场景演示
- 增强多样化场景演示
- 杂乱多样化场景演示
- 场景多样性分析

#### 2. 录制模式
```bash
python simpler_env_demo.py --mode record
```
录制自定义环境的视频和图像。

#### 3. 完整模式
```bash
python simpler_env_demo.py --mode all
```
运行演示 + 录制的完整功能。

#### 4. 自定义输出目录
```bash
python simpler_env_demo.py --mode demo --output-dir /custom/path
```

### 命令行参数

- `--mode`: 运行模式 (`demo`, `record`, `all`)
- `--output-dir`: 自定义输出目录路径

## 📊 输出说明

### 演示模式输出

运行演示模式后，您将看到：

1. **实时控制台输出**: 显示每个步骤的执行状态
2. **图像文件**: 保存在 `images/` 目录下，按场景类型分类
3. **演示报告**: JSON格式的详细报告，包含所有演示结果

### 录制模式输出

录制模式会生成：

1. **视频文件**: MP4格式的环境交互视频
2. **关键帧图像**: 每个episode的第一帧和最后一帧
3. **录制报告**: 包含录制详情的JSON文件

## 🔧 核心类和方法

### SimplerEnvDemo 类

主要的演示类，包含以下核心方法：

- `setup_environment()`: 设置SimplerEnv环境
- `demo_basic_scene()`: 演示基础多样化场景
- `demo_enhanced_scene()`: 演示增强多样化场景
- `demo_cluttered_scene()`: 演示杂乱多样化场景
- `analyze_scene_diversity()`: 分析场景多样性
- `record_environment_video()`: 录制环境视频
- `run_all_demos()`: 运行所有演示

## 🎨 支持的环境

系统支持以下自定义环境：

- `custom_diverse_pick_scene`: 基础多样化拾取场景
- `custom_enhanced_low_diversity`: 低多样性增强场景
- `custom_enhanced_medium_diversity`: 中等多样性增强场景
- `custom_enhanced_high_diversity`: 高多样性增强场景

## 📈 示例输出

### 成功运行示例

```
📁 输出目录设置为: /home/<USER>/claude/SpatialVLA/Z/Z_new_trial
🎯 运行演示模式

🚀 SimplerEnv 统一演示
============================================================
🔧 设置SimplerEnv环境...
✅ SimplerEnv环境设置完成

🎯 演示基础多样化场景
==================================================
✅ 环境创建成功
✅ 环境重置成功
✅ 图像已保存: /home/<USER>/claude/SpatialVLA/Z/Z_new_trial/images/basic_scenes/basic_scene_demo.png
📦 场景中有 3 个物体:
   1. yellow_cube_3cm_target
   2. pepsi_can_target
   3. fanta_can_distractor
✅ 基础场景演示完成

🎉 所有演示完成！
📁 输出目录: /home/<USER>/claude/SpatialVLA/Z/Z_new_trial
📊 演示报告: /home/<USER>/claude/SpatialVLA/Z/Z_new_trial/reports/demo_report_*.json
```

## 🔍 故障排除

### 常见问题

1. **环境变量未设置**
   ```bash
   export MS2_REAL2SIM_ASSET_DIR=/path/to/SimplerEnv/ManiSkill2_real2sim/data
   ```

2. **CUDA/GPU 问题**
   ```bash
   export CUDA_VISIBLE_DEVICES=0
   ```

3. **显示问题**
   ```bash
   export MUJOCO_GL=egl
   ```

4. **权限问题**
   确保输出目录有写入权限：
   ```bash
   chmod 755 /home/<USER>/claude/SpatialVLA/Z/Z_new_trial
   ```

## 🚀 与原版本的改进

### 代码组织
- ✅ 将分散的功能整合到单一脚本
- ✅ 移除重复和测试文件
- ✅ 统一输出目录管理
- ✅ 标准化错误处理

### 用户体验
- ✅ 简化的命令行接口
- ✅ 清晰的进度显示
- ✅ 详细的输出报告
- ✅ 灵活的配置选项

### 维护性
- ✅ 模块化的类设计
- ✅ 完善的文档注释
- ✅ 统一的编码风格
- ✅ 易于扩展的架构

## 📝 更新日志

### v2.0 (当前版本)
- 重新组织代码结构
- 创建统一演示脚本
- 更新输出目录到 Z_new_trial
- 清理冗余文件
- 改进文档和使用指南

### v1.0 (原版本)
- 基础多样化场景实现
- 分散的演示脚本
- 多个输出目录

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源。

## 📞 联系方式

如有问题或建议，请创建 Issue 或联系项目维护者。
