#!/usr/bin/env python3
"""
SimplerEnv 统一演示脚本

这个脚本整合了SimplerEnv的所有核心演示功能，包括：
- 基础多样化场景演示
- 增强多样化场景演示  
- 杂乱场景演示
- 场景多样性分析
- 场景录制和视频生成
- 图像捕获和保存

输出目录: /home/<USER>/claude/SpatialVLA/Z/Z_new_trial
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path
import json
from datetime import datetime
import time
from typing import Dict, List, Optional, Tuple, Any
import argparse

# 设置环境变量
os.environ['MUJOCO_GL'] = 'egl'
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 添加路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / "SimplerEnv" / "ManiSkill2_real2sim"))
sys.path.insert(0, str(current_dir / "SimplerEnv"))

# 输出目录配置
OUTPUT_BASE_DIR = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")


class SimplerEnvDemo:
    """SimplerEnv统一演示类"""
    
    def __init__(self, output_dir: Optional[Path] = None):
        """初始化演示类"""
        self.output_dir = output_dir or OUTPUT_BASE_DIR
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        self.images_dir = self.output_dir / "images"
        self.videos_dir = self.output_dir / "videos"
        self.reports_dir = self.output_dir / "reports"
        
        for dir_path in [self.images_dir, self.videos_dir, self.reports_dir]:
            dir_path.mkdir(exist_ok=True)
        
        print(f"📁 输出目录设置为: {self.output_dir}")
        print(f"   - 图像目录: {self.images_dir}")
        print(f"   - 视频目录: {self.videos_dir}")
        print(f"   - 报告目录: {self.reports_dir}")
    
    def setup_environment(self):
        """设置SimplerEnv环境"""
        print("🔧 设置SimplerEnv环境...")
        
        try:
            # 设置资源目录
            asset_dir = current_dir / "SimplerEnv" / "ManiSkill2_real2sim" / "data"
            if asset_dir.exists():
                os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(asset_dir)
            
            # 导入必要模块
            import gymnasium as gym
            import simpler_env
            import sapien.core as sapien
            
            # 关闭降噪以避免内核崩溃
            sapien.render_config.rt_use_denoiser = False
            
            # 强制导入自定义环境模块
            from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes import diverse_scene_env
            from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes import enhanced_diverse_env
            
            print("✅ SimplerEnv环境设置完成")
            return True
            
        except Exception as e:
            print(f"❌ 环境设置失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_image_from_obs(self, env, obs) -> np.ndarray:
        """从观察中提取图像"""
        try:
            # 尝试不同的图像键
            if 'image' in obs and 'overhead_camera' in obs['image']:
                camera_obs = obs['image']['overhead_camera']
                if 'Color' in camera_obs:
                    return camera_obs['Color']
                elif 'rgb' in camera_obs:
                    return camera_obs['rgb']
                elif 'color' in camera_obs:
                    return camera_obs['color']
            
            # 如果上述都不行，尝试其他相机
            if 'image' in obs:
                for camera_name, camera_data in obs['image'].items():
                    if isinstance(camera_data, dict):
                        for key in ['Color', 'rgb', 'color']:
                            if key in camera_data:
                                return camera_data[key]
            
            print("⚠ 无法找到图像数据，返回空图像")
            return np.zeros((480, 640, 3), dtype=np.uint8)
            
        except Exception as e:
            print(f"⚠ 图像提取失败: {e}")
            return np.zeros((480, 640, 3), dtype=np.uint8)
    
    def save_image(self, image: np.ndarray, filename: str, subdir: str = "") -> Path:
        """保存图像到指定目录"""
        try:
            # 确定保存路径
            if subdir:
                save_dir = self.images_dir / subdir
                save_dir.mkdir(exist_ok=True)
            else:
                save_dir = self.images_dir
            
            image_path = save_dir / filename
            
            # 处理图像格式
            if image.dtype == np.float32 or image.dtype == np.float64:
                image_uint8 = (image * 255).astype(np.uint8)
            else:
                image_uint8 = image
            
            # 转换颜色格式并保存
            if len(image_uint8.shape) == 3 and image_uint8.shape[2] >= 3:
                image_bgr = cv2.cvtColor(image_uint8[:,:,:3], cv2.COLOR_RGB2BGR)
            else:
                image_bgr = image_uint8
            
            cv2.imwrite(str(image_path), image_bgr)
            print(f"✅ 图像已保存: {image_path}")
            return image_path
            
        except Exception as e:
            print(f"❌ 图像保存失败: {e}")
            return None


    def save_video(self, images: List[np.ndarray], filename: str, fps: int = 10, subdir: str = "") -> Optional[Path]:
        """保存图像序列为视频"""
        if not images:
            print(f"⚠ 没有图像可保存到 {filename}")
            return None

        try:
            # 确定保存路径
            if subdir:
                save_dir = self.videos_dir / subdir
                save_dir.mkdir(exist_ok=True)
            else:
                save_dir = self.videos_dir

            video_path = save_dir / filename

            height, width, channels = images[0].shape
            size = (width, height)

            # 使用mp4v编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(video_path), fourcc, fps, size)

            for image in images:
                # 确保图像是正确的格式
                if len(image.shape) == 3 and image.shape[2] == 3:
                    # 转换RGB到BGR
                    bgr_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                else:
                    bgr_image = image

                out.write(bgr_image)

            out.release()
            print(f"✅ 视频已保存到: {video_path}")
            return video_path

        except Exception as e:
            print(f"❌ 视频保存失败: {e}")
            return None

    def demo_basic_scene(self) -> Dict[str, Any]:
        """演示基础多样化场景"""
        print("\n🎯 演示基础多样化场景")
        print("=" * 50)

        try:
            from mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env import DiverseSceneEnv

            # 创建环境
            env = DiverseSceneEnv(
                robot="google_robot_static",
                scene_name="dummy_tabletop",
                obs_mode="image",
                camera_cfgs={"add_segmentation": True},
                render_mode="cameras",
                num_objects_range=(3, 5),
            )

            print("✅ 环境创建成功")

            # 重置并获取观察
            obs, info = env.reset()
            print("✅ 环境重置成功")

            # 获取并保存图像
            image = self.get_image_from_obs(env, obs)
            image_path = self.save_image(image, "basic_scene_demo.png", "basic_scenes")

            # 收集场景信息
            scene_info = {
                "scene_type": "basic_diverse",
                "timestamp": datetime.now().isoformat(),
                "image_path": str(image_path) if image_path else None,
                "image_shape": image.shape,
                "objects": []
            }

            # 显示场景信息
            if hasattr(env, 'all_scene_objects'):
                print(f"📦 场景中有 {len(env.all_scene_objects)} 个物体:")
                for i, obj in enumerate(env.all_scene_objects, 1):
                    obj_info = {"id": i, "name": obj.name}
                    scene_info["objects"].append(obj_info)
                    print(f"   {i}. {obj.name}")

            env.close()
            print("✅ 基础场景演示完成")
            return scene_info

        except Exception as e:
            print(f"❌ 基础场景演示失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

    def demo_enhanced_scene(self, num_variations: int = 3) -> Dict[str, Any]:
        """演示增强多样化场景（包含变化）"""
        print(f"\n🌟 演示增强多样化场景 ({num_variations}个变化)")
        print("=" * 50)

        try:
            from mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env import DiverseEnhancedSceneEnv

            # 创建环境
            env = DiverseEnhancedSceneEnv(
                robot="google_robot_static",
                scene_name="dummy_tabletop",
                obs_mode="image",
                camera_cfgs={"add_segmentation": True},
                render_mode="cameras",
                num_objects_range=(4, 6),
            )

            print("✅ 增强环境创建成功")

            variations_info = {
                "scene_type": "enhanced_diverse",
                "timestamp": datetime.now().isoformat(),
                "num_variations": num_variations,
                "variations": []
            }

            # 测试多次重置以展示变化
            for i in range(num_variations):
                print(f"\n--- 变化演示 {i+1} ---")
                obs, info = env.reset()
                print(f"✅ 重置 {i+1} 成功 (应用了随机变化)")

                # 保存图像
                image = self.get_image_from_obs(env, obs)
                filename = f"enhanced_scene_variation_{i+1}.png"
                image_path = self.save_image(image, filename, "enhanced_scenes")

                variation_info = {
                    "variation_id": i+1,
                    "image_path": str(image_path) if image_path else None,
                    "image_shape": image.shape
                }
                variations_info["variations"].append(variation_info)

            env.close()
            print("✅ 增强场景演示完成")
            return variations_info

        except Exception as e:
            print(f"❌ 增强场景演示失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

    def demo_cluttered_scene(self) -> Dict[str, Any]:
        """演示杂乱多样化场景"""
        print("\n🏗️ 演示杂乱多样化场景")
        print("=" * 50)

        try:
            from mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env import DiverseClutteredSceneEnv

            # 创建环境
            env = DiverseClutteredSceneEnv(
                robot="google_robot_static",
                scene_name="dummy_tabletop",
                obs_mode="image",
                camera_cfgs={"add_segmentation": True},
                render_mode="cameras",
                num_objects_range=(6, 8),
            )

            print("✅ 杂乱环境创建成功")

            # 重置并获取观察
            obs, _ = env.reset()
            print("✅ 环境重置成功")

            # 获取并保存图像
            image = self.get_image_from_obs(env, obs)
            image_path = self.save_image(image, "cluttered_scene_demo.png", "cluttered_scenes")

            # 收集场景信息
            scene_info = {
                "scene_type": "cluttered_diverse",
                "timestamp": datetime.now().isoformat(),
                "image_path": str(image_path) if image_path else None,
                "image_shape": image.shape,
                "objects": []
            }

            # 显示场景信息
            if hasattr(env, 'all_scene_objects'):
                print(f"📦 杂乱场景中有 {len(env.all_scene_objects)} 个物体:")
                for i, obj in enumerate(env.all_scene_objects, 1):
                    obj_info = {"id": i, "name": obj.name}
                    scene_info["objects"].append(obj_info)
                    print(f"   {i}. {obj.name}")

            env.close()
            print("✅ 杂乱场景演示完成")
            return scene_info

        except Exception as e:
            print(f"❌ 杂乱场景演示失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

    def analyze_scene_diversity(self, num_samples: int = 10) -> Dict[str, Any]:
        """分析场景多样性"""
        print(f"\n📊 分析场景多样性 (基于{num_samples}次重置)")
        print("=" * 50)

        try:
            from mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env import DiverseEnhancedSceneEnv

            # 测试不同的物体数量范围
            test_configs = [
                {"range": (3, 5), "name": "少量物体"},
                {"range": (6, 10), "name": "中等物体"},
                {"range": (10, 15), "name": "大量物体"}
            ]

            all_results = {}

            for config in test_configs:
                print(f"\n🔍 测试配置: {config['name']} {config['range']}")

                env = DiverseEnhancedSceneEnv(
                    robot="google_robot_static",
                    scene_name="dummy_tabletop",
                    obs_mode="image",
                    camera_cfgs={"add_segmentation": True},
                    render_mode="cameras",
                    num_objects_range=config['range'],
                )

                # 收集多次重置的统计信息
                object_counts = []
                unique_objects = set()
                actual_counts = []

                for i in range(5):  # 每个配置测试5次
                    obs, info = env.reset()

                    # 获取配置信息
                    scene_config = info.get("scene_config", {})
                    configured_count = scene_config.get("total_objects", 0)

                    # 获取实际物体数量
                    actual_count = 0
                    if hasattr(env, 'all_scene_objects'):
                        actual_count = len(env.all_scene_objects)
                        for obj in env.all_scene_objects:
                            unique_objects.add(obj.name)

                    object_counts.append(configured_count)
                    actual_counts.append(actual_count)

                    print(f"  重置 {i+1}: 配置{configured_count}个 / 实际{actual_count}个物体")

                    # 保存一张示例图像
                    if i == 0:
                        image = self.get_image_from_obs(env, obs)
                        filename = f"diversity_test_{config['name'].replace(' ', '_')}.png"
                        image_path = self.save_image(image, filename, "diversity_analysis")

                env.close()

                # 记录结果
                all_results[config['name']] = {
                    "range": config['range'],
                    "configured_counts": object_counts,
                    "actual_counts": actual_counts,
                    "unique_objects": len(unique_objects),
                    "avg_configured": np.mean(object_counts) if object_counts else 0,
                    "avg_actual": np.mean(actual_counts) if actual_counts else 0
                }

            # 计算统计结果
            diversity_info = {
                "analysis_type": "scene_diversity",
                "timestamp": datetime.now().isoformat(),
                "num_samples": num_samples,
                "test_configs": all_results,
                "summary": {
                    "total_unique_objects": len(unique_objects),
                    "object_types": sorted(list(unique_objects))
                }
            }

            # 显示统计结果
            print(f"📈 多样性分析结果:")
            for config_name, results in all_results.items():
                print(f"\n  🔸 {config_name} {results['range']}:")
                print(f"    - 配置物体数量: {min(results['configured_counts'])}-{max(results['configured_counts'])}")
                print(f"    - 实际物体数量: {min(results['actual_counts'])}-{max(results['actual_counts'])}")
                print(f"    - 平均配置: {results['avg_configured']:.1f}")
                print(f"    - 平均实际: {results['avg_actual']:.1f}")

            print(f"\n  📊 总体统计:")
            print(f"    - 独特物体类型: {diversity_info['summary']['total_unique_objects']}")
            print(f"    - 物体类型: {', '.join(diversity_info['summary']['object_types'][:5])}{'...' if len(diversity_info['summary']['object_types']) > 5 else ''}")
            print("✅ 多样性分析完成")

            return diversity_info

        except Exception as e:
            print(f"❌ 多样性分析失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

    def record_environment_video(self, env_name: str, num_episodes: int = 2, steps_per_episode: int = 30) -> Dict[str, Any]:
        """录制环境视频"""
        print(f"\n🎬 录制环境视频: {env_name}")
        print("=" * 50)

        try:
            import simpler_env

            # 创建环境
            env = simpler_env.make(env_name)
            print(f"✅ 环境 {env_name} 创建成功")

            recording_info = {
                "environment_name": env_name,
                "timestamp": datetime.now().isoformat(),
                "num_episodes": num_episodes,
                "steps_per_episode": steps_per_episode,
                "episodes": []
            }

            for episode in range(num_episodes):
                print(f"\n--- 录制 Episode {episode + 1}/{num_episodes} ---")

                # 重置环境
                seed = episode * 1000 + 42
                obs, reset_info = env.reset(seed=seed)

                # 获取语言指令
                try:
                    instruction = env.get_language_instruction()
                    print(f"任务指令: {instruction}")
                except:
                    instruction = "Pick up an object"
                    print(f"默认指令: {instruction}")

                # 收集图像
                images = []
                initial_image = self.get_image_from_obs(env, obs)
                images.append(initial_image)

                # 运行episode
                for step in range(steps_per_episode):
                    action = env.action_space.sample()
                    obs, reward, terminated, truncated, info = env.step(action)

                    image = self.get_image_from_obs(env, obs)
                    images.append(image)

                    if terminated or truncated:
                        print(f"Episode在第 {step + 1} 步结束")
                        break

                # 保存视频
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                video_filename = f"{env_name}_episode_{episode + 1:02d}_{timestamp}.mp4"
                video_path = self.save_video(images, video_filename, fps=10, subdir=env_name)

                # 保存第一帧和最后一帧
                if images:
                    first_frame_path = self.save_image(
                        images[0],
                        f"{env_name}_episode_{episode + 1:02d}_first_frame.png",
                        subdir=env_name
                    )
                    last_frame_path = self.save_image(
                        images[-1],
                        f"{env_name}_episode_{episode + 1:02d}_last_frame.png",
                        subdir=env_name
                    )

                episode_info = {
                    "episode": episode + 1,
                    "seed": seed,
                    "steps": len(images) - 1,
                    "instruction": instruction,
                    "video_file": str(video_path) if video_path else None,
                    "total_frames": len(images),
                    "timestamp": timestamp
                }
                recording_info["episodes"].append(episode_info)

                print(f"✅ Episode {episode + 1} 录制完成: {len(images)} 帧")

            env.close()
            print(f"✅ 环境 {env_name} 录制完成")
            return recording_info

        except Exception as e:
            print(f"❌ 环境 {env_name} 录制失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}

    def run_all_demos(self) -> Dict[str, Any]:
        """运行所有演示"""
        print("\n🚀 SimplerEnv 统一演示")
        print("=" * 60)
        print("本演示将展示SimplerEnv的所有核心功能")
        print("=" * 60)

        # 设置环境
        if not self.setup_environment():
            return {"error": "环境设置失败"}

        results = {
            "demo_session": {
                "timestamp": datetime.now().isoformat(),
                "output_directory": str(self.output_dir)
            },
            "demos": {}
        }

        try:
            # 1. 基础场景演示
            print("\n" + "="*60)
            results["demos"]["basic_scene"] = self.demo_basic_scene()

            # 2. 增强场景演示
            print("\n" + "="*60)
            results["demos"]["enhanced_scene"] = self.demo_enhanced_scene()

            # 3. 杂乱场景演示
            print("\n" + "="*60)
            results["demos"]["cluttered_scene"] = self.demo_cluttered_scene()

            # 4. 多样性分析
            print("\n" + "="*60)
            results["demos"]["diversity_analysis"] = self.analyze_scene_diversity()

            # 保存演示报告
            report_path = self.reports_dir / f"demo_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)

            print(f"\n🎉 所有演示完成！")
            print(f"📁 输出目录: {self.output_dir}")
            print(f"📊 演示报告: {report_path}")

            # 显示生成的文件
            self._show_generated_files()

            return results

        except Exception as e:
            print(f"❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            results["error"] = str(e)
            return results

    def _show_generated_files(self):
        """显示生成的文件"""
        print(f"\n📋 生成的文件:")

        # 显示图像文件
        image_files = list(self.images_dir.rglob("*.png"))
        if image_files:
            print(f"   📸 图像文件 ({len(image_files)} 个):")
            for img_file in sorted(image_files):
                rel_path = img_file.relative_to(self.output_dir)
                print(f"      - {rel_path}")

        # 显示视频文件
        video_files = list(self.videos_dir.rglob("*.mp4"))
        if video_files:
            print(f"   🎬 视频文件 ({len(video_files)} 个):")
            for vid_file in sorted(video_files):
                rel_path = vid_file.relative_to(self.output_dir)
                print(f"      - {rel_path}")

        # 显示报告文件
        report_files = list(self.reports_dir.rglob("*.json"))
        if report_files:
            print(f"   📊 报告文件 ({len(report_files)} 个):")
            for rep_file in sorted(report_files):
                rel_path = rep_file.relative_to(self.output_dir)
                print(f"      - {rel_path}")

    def record_custom_environments(self) -> Dict[str, Any]:
        """录制自定义环境"""
        print("\n🎬 录制自定义多样化场景")
        print("=" * 50)

        # 要录制的环境列表
        environments_to_record = [
            "custom_diverse_pick_scene",
            "custom_enhanced_low_diversity",
            "custom_enhanced_medium_diversity",
            "custom_enhanced_high_diversity"
        ]

        recording_results = {
            "recording_session": {
                "timestamp": datetime.now().isoformat(),
                "total_environments": len(environments_to_record)
            },
            "environments": {}
        }

        successful_recordings = 0

        for env_name in environments_to_record:
            print(f"\n🎬 开始录制环境: {env_name}")

            try:
                result = self.record_environment_video(
                    env_name=env_name,
                    num_episodes=2,
                    steps_per_episode=20
                )

                if "error" not in result:
                    recording_results["environments"][env_name] = {
                        "status": "success",
                        "result": result
                    }
                    successful_recordings += 1
                    print(f"✅ {env_name} 录制成功")
                else:
                    recording_results["environments"][env_name] = {
                        "status": "failed",
                        "error": result["error"]
                    }
                    print(f"❌ {env_name} 录制失败")

            except Exception as e:
                recording_results["environments"][env_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ {env_name} 录制异常: {e}")

        recording_results["recording_session"]["successful_recordings"] = successful_recordings
        recording_results["recording_session"]["failed_recordings"] = len(environments_to_record) - successful_recordings

        # 保存录制报告
        report_path = self.reports_dir / f"recording_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(recording_results, f, indent=2, ensure_ascii=False, default=str)

        print(f"\n🎬 录制完成总结")
        print(f"成功录制环境: {successful_recordings}/{len(environments_to_record)}")
        print(f"📊 录制报告: {report_path}")

        return recording_results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SimplerEnv 统一演示脚本")
    parser.add_argument("--mode", choices=["demo", "record", "all"], default="demo",
                       help="运行模式: demo(仅演示), record(仅录制), all(演示+录制)")
    parser.add_argument("--output-dir", type=str, default=None,
                       help="输出目录路径")

    args = parser.parse_args()

    # 创建演示实例
    output_dir = Path(args.output_dir) if args.output_dir else OUTPUT_BASE_DIR
    demo = SimplerEnvDemo(output_dir)

    try:
        if args.mode == "demo":
            print("🎯 运行演示模式")
            results = demo.run_all_demos()

        elif args.mode == "record":
            print("🎬 运行录制模式")
            if demo.setup_environment():
                results = demo.record_custom_environments()
            else:
                print("❌ 环境设置失败")
                return 1

        elif args.mode == "all":
            print("🚀 运行完整模式 (演示 + 录制)")
            # 先运行演示
            demo_results = demo.run_all_demos()
            # 再运行录制
            record_results = demo.record_custom_environments()

            results = {
                "mode": "all",
                "demo_results": demo_results,
                "record_results": record_results
            }

        if "error" not in results:
            print(f"\n🎉 {args.mode} 模式执行完成！")
            print(f"📁 所有输出保存在: {demo.output_dir}")
            return 0
        else:
            print(f"\n❌ {args.mode} 模式执行失败")
            return 1

    except KeyboardInterrupt:
        print("\n⚠ 用户中断执行")
        return 1
    except Exception as e:
        print(f"\n❌ 执行过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
