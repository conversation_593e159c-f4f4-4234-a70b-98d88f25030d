#!/usr/bin/env python3
"""
强制生成10个物体的SimplerEnv场景

这个脚本绕过子类的默认参数限制，强制生成包含10个物体的场景。
"""

import os
import sys
import numpy as np
from pathlib import Path
from datetime import datetime

# 设置环境变量
os.environ['MUJOCO_GL'] = 'egl'
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 添加路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / "SimplerEnv" / "ManiSkill2_real2sim"))
sys.path.insert(0, str(current_dir / "SimplerEnv"))

def force_create_10_objects_scene():
    """强制创建包含10个物体的场景"""
    print("🎯 强制创建包含10个物体的SimplerEnv场景")
    print("=" * 60)
    
    try:
        import gymnasium as gym
        import sapien.core as sapien
        from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes import enhanced_diverse_env
        from diversity_enhancer import DiversityEnhancer
        import cv2
        
        # 关闭降噪
        sapien.render_config.rt_use_denoiser = False
        
        print("✅ 模块导入成功")
        
        # 创建多样性增强器
        enhancer = DiversityEnhancer(
            seed=42,
            material_intensity="dramatic",
            lighting_intensity="medium"
        )
        
        print("📦 强制配置10个物体参数...")
        
        # 直接使用基类，避免子类覆盖参数
        env = enhanced_diverse_env.EnhancedDiverseSceneEnv(
            robot="google_robot_static",
            scene_name="dummy_tabletop",
            obs_mode="image",
            camera_cfgs={"add_segmentation": True},
            render_mode="cameras",
            num_objects_range=(10, 10),  # 强制10个物体
            diversity_level="custom",    # 避免使用预定义级别
            lighting_variation_prob=0.9,
            material_variation_prob=1.0,
            physics_variation_prob=0.8,
        )
        
        # 手动设置多样性增强器
        env.diversity_enhancer = enhancer
        
        # 验证参数是否正确设置
        print(f"✅ 环境创建成功")
        print(f"📊 配置的物体数量范围: {env.num_objects_range}")
        print(f"📊 多样性级别: {env.diversity_level}")
        
        # 重置环境多次，确保生成10个物体
        max_attempts = 5
        for attempt in range(max_attempts):
            print(f"\n🔄 尝试 {attempt + 1}/{max_attempts} 生成10个物体场景...")
            
            obs, info = env.reset()
            
            # 检查实际生成的物体数量
            try:
                actors = env.get_actors()
                # 过滤掉机器人和桌子，只计算场景物体
                scene_objects = [actor for actor in actors if 
                               'robot' not in actor.name.lower() and 
                               'table' not in actor.name.lower() and
                               'ground' not in actor.name.lower() and
                               'wall' not in actor.name.lower()]
                
                actual_count = len(scene_objects)
                print(f"   实际生成物体数量: {actual_count}")
                print(f"   物体列表: {[obj.name for obj in scene_objects]}")
                
                if actual_count >= 8:  # 接受8个或以上
                    print(f"✅ 成功生成 {actual_count} 个物体的场景！")
                    
                    # 获取图像
                    def get_image_from_obs(obs):
                        if 'image' in obs and 'overhead_camera' in obs['image']:
                            camera_obs = obs['image']['overhead_camera']
                            if 'Color' in camera_obs:
                                return camera_obs['Color']
                        return np.zeros((480, 640, 3), dtype=np.uint8)
                    
                    image = get_image_from_obs(obs)
                    
                    # 保存图像
                    output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
                    output_dir.mkdir(parents=True, exist_ok=True)
                    
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    image_filename = f"forced_{actual_count}_objects_scene_{timestamp}.png"
                    image_path = output_dir / image_filename
                    
                    # 转换图像格式并保存
                    if image.dtype == np.float32 or image.dtype == np.float64:
                        image = (image * 255).astype(np.uint8)
                    
                    # 转换RGB到BGR用于OpenCV
                    image_bgr = cv2.cvtColor(image[:,:,:3], cv2.COLOR_RGB2BGR)
                    cv2.imwrite(str(image_path), image_bgr)
                    
                    print(f"✅ {actual_count}个物体场景图像已保存: {image_path}")
                    
                    # 显示详细信息
                    print(f"\n📊 {actual_count}个物体场景详情:")
                    print("-" * 40)
                    for i, obj in enumerate(scene_objects, 1):
                        print(f"   {i}. {obj.name}")
                    
                    env.close()
                    return str(image_path), actual_count
                else:
                    print(f"⚠️ 只生成了 {actual_count} 个物体，继续尝试...")
                    
            except Exception as e:
                print(f"⚠️ 无法获取物体信息: {e}")
        
        print(f"\n❌ 经过 {max_attempts} 次尝试，仍无法生成足够的物体")
        env.close()
        return None, 0
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None, 0

def analyze_object_generation_limits():
    """分析物体生成的限制因素"""
    print("\n🔍 分析物体生成限制因素:")
    print("-" * 40)
    
    try:
        from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes.custom_scene_config import CustomSceneConfig
        
        # 检查场景配置的限制
        config = CustomSceneConfig()
        
        print("📋 场景配置分析:")
        print(f"   - 工作空间大小: 可能限制物体数量")
        print(f"   - 物体间最小距离: 影响可放置的物体数量")
        print(f"   - 碰撞检测: 可能阻止物体生成")
        print(f"   - 桌面空间: 物理限制")
        
        print("\n💡 可能的解决方案:")
        print("   1. 减小物体尺寸")
        print("   2. 减小物体间最小距离")
        print("   3. 扩大工作空间")
        print("   4. 使用更大的桌面")
        
    except Exception as e:
        print(f"   无法分析配置: {e}")

def main():
    """主函数"""
    print("🎯 SimplerEnv 强制10个物体场景生成器")
    print("=" * 50)
    
    # 分析限制因素
    analyze_object_generation_limits()
    
    print("\n🚀 开始强制生成多物体场景...")
    
    # 尝试生成场景
    result_path, object_count = force_create_10_objects_scene()
    
    if result_path:
        print(f"\n🎉 成功生成包含 {object_count} 个物体的场景！")
        print(f"📸 图像路径: {result_path}")
        print("🎨 场景特点:")
        print(f"   - 包含 {object_count} 个不同类型的物体")
        print("   - 戏剧性材质变化效果")
        print("   - 中等光照变化")
        print("   - 高概率物理属性变化")
    else:
        print("\n❌ 无法生成足够多物体的场景")
        print("💡 建议:")
        print("   - 检查场景配置参数")
        print("   - 尝试使用更大的工作空间")
        print("   - 减小物体尺寸或间距要求")

if __name__ == "__main__":
    main()
