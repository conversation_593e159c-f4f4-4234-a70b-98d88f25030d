#!/usr/bin/env python3
"""
分析SimplerEnv中物体的可见性问题

这个脚本分析为什么代码报告有10个物体，但图像中只能看到少数几个。
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path
from datetime import datetime
import json

# 设置环境变量
os.environ['MUJOCO_GL'] = 'egl'
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 添加路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / "SimplerEnv" / "ManiSkill2_real2sim"))
sys.path.insert(0, str(current_dir / "SimplerEnv"))

def analyze_object_positions_and_visibility():
    """分析物体位置和可见性"""
    print("🔍 分析SimplerEnv物体位置和可见性")
    print("=" * 50)
    
    try:
        # 使用工作的导入方式
        import gymnasium as gym
        import sapien.core as sapien
        
        # 关闭降噪
        sapien.render_config.rt_use_denoiser = False
        
        # 设置SimplerEnv环境
        sys.path.insert(0, str(current_dir / "SimplerEnv"))
        import simpler_env
        from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict
        
        print("✅ 模块导入成功")
        
        # 创建环境 - 使用SimplerEnv的标准方式
        env = simpler_env.make('google_robot_pick_object')  # 这个环境支持多物体
        
        print("✅ 环境创建成功")
        
        # 重置环境多次分析
        analysis_results = []
        
        for trial in range(3):
            print(f"\n🔄 试验 {trial + 1}/3")
            print("-" * 30)
            
            obs, reset_info = env.reset()
            instruction = env.get_language_instruction()
            
            print(f"📝 任务指令: {instruction}")
            print(f"📊 重置信息: {reset_info}")
            
            # 获取图像
            image = get_image_from_maniskill2_obs_dict(env, obs)
            
            # 分析物体信息
            actors = env.get_actors()
            print(f"\n🎭 场景中的所有Actor ({len(actors)} 个):")
            
            scene_objects = []
            robot_parts = []
            environment_parts = []
            
            for i, actor in enumerate(actors):
                actor_name = actor.name
                actor_pose = actor.get_pose()
                position = actor_pose.p
                
                print(f"   {i+1:2d}. {actor_name}")
                print(f"       位置: ({position[0]:.3f}, {position[1]:.3f}, {position[2]:.3f})")
                
                # 分类Actor
                if any(keyword in actor_name.lower() for keyword in ['robot', 'gripper', 'link']):
                    robot_parts.append(actor_name)
                elif any(keyword in actor_name.lower() for keyword in ['table', 'ground', 'wall', 'arena']):
                    environment_parts.append(actor_name)
                else:
                    scene_objects.append({
                        'name': actor_name,
                        'position': position,
                        'height': position[2]
                    })
            
            print(f"\n📊 Actor分类统计:")
            print(f"   - 场景物体: {len(scene_objects)} 个")
            print(f"   - 机器人部件: {len(robot_parts)} 个")
            print(f"   - 环境部件: {len(environment_parts)} 个")
            
            # 分析场景物体的可见性
            print(f"\n🔍 场景物体详细分析:")
            visible_objects = []
            hidden_objects = []
            
            for obj in scene_objects:
                pos = obj['position']
                height = obj['height']
                
                # 判断物体是否可能可见
                # 1. 在桌面上方 (z > 0.8)
                # 2. 在相机视野范围内
                is_above_table = height > 0.8
                is_in_camera_range = (-0.6 < pos[0] < 0.6) and (-0.6 < pos[1] < 0.6)
                
                if is_above_table and is_in_camera_range:
                    visible_objects.append(obj)
                    status = "✅ 可见"
                else:
                    hidden_objects.append(obj)
                    if not is_above_table:
                        status = "❌ 掉落 (桌面下)"
                    else:
                        status = "❌ 超出视野"
                
                print(f"     {obj['name']}: {status}")
                print(f"       位置: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
            
            # 保存图像用于验证
            output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            image_filename = f"visibility_analysis_trial_{trial+1}_{timestamp}.png"
            image_path = output_dir / image_filename
            
            # 转换图像格式并保存
            if image.dtype == np.float32 or image.dtype == np.float64:
                image = (image * 255).astype(np.uint8)
            
            # 确保是3通道
            if len(image.shape) == 3 and image.shape[2] >= 3:
                image_bgr = cv2.cvtColor(image[:,:,:3], cv2.COLOR_RGB2BGR)
            else:
                image_bgr = image
            
            cv2.imwrite(str(image_path), image_bgr)
            
            # 记录分析结果
            trial_result = {
                'trial': trial + 1,
                'instruction': instruction,
                'total_actors': len(actors),
                'scene_objects': len(scene_objects),
                'visible_objects': len(visible_objects),
                'hidden_objects': len(hidden_objects),
                'image_path': str(image_path),
                'object_details': scene_objects
            }
            
            analysis_results.append(trial_result)
            
            print(f"\n📈 试验 {trial + 1} 总结:")
            print(f"   - 总Actor数: {len(actors)}")
            print(f"   - 场景物体数: {len(scene_objects)}")
            print(f"   - 可见物体数: {len(visible_objects)}")
            print(f"   - 隐藏物体数: {len(hidden_objects)}")
            print(f"   - 图像已保存: {image_filename}")
        
        # 生成综合分析报告
        generate_comprehensive_report(analysis_results)
        
        env.close()
        return analysis_results
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_comprehensive_report(results):
    """生成综合分析报告"""
    print(f"\n📋 综合分析报告")
    print("=" * 50)
    
    if not results:
        print("❌ 没有分析结果")
        return
    
    # 统计信息
    total_trials = len(results)
    avg_scene_objects = np.mean([r['scene_objects'] for r in results])
    avg_visible_objects = np.mean([r['visible_objects'] for r in results])
    avg_hidden_objects = np.mean([r['hidden_objects'] for r in results])
    
    print(f"🔢 统计摘要 (基于 {total_trials} 次试验):")
    print(f"   - 平均场景物体数: {avg_scene_objects:.1f}")
    print(f"   - 平均可见物体数: {avg_visible_objects:.1f}")
    print(f"   - 平均隐藏物体数: {avg_hidden_objects:.1f}")
    print(f"   - 可见率: {avg_visible_objects/avg_scene_objects*100:.1f}%")
    
    # 分析隐藏原因
    print(f"\n🔍 物体隐藏原因分析:")
    fall_count = 0
    out_of_view_count = 0
    
    for result in results:
        for obj in result['object_details']:
            pos = obj['position']
            height = obj['height']
            
            is_above_table = height > 0.8
            is_in_camera_range = (-0.6 < pos[0] < 0.6) and (-0.6 < pos[1] < 0.6)
            
            if not is_above_table:
                fall_count += 1
            elif not is_in_camera_range:
                out_of_view_count += 1
    
    total_hidden = fall_count + out_of_view_count
    if total_hidden > 0:
        print(f"   - 掉落到桌面下: {fall_count} 个 ({fall_count/total_hidden*100:.1f}%)")
        print(f"   - 超出相机视野: {out_of_view_count} 个 ({out_of_view_count/total_hidden*100:.1f}%)")
    
    # 保存详细报告
    output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
    report_path = output_dir / f"visibility_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_trials': total_trials,
            'avg_scene_objects': avg_scene_objects,
            'avg_visible_objects': avg_visible_objects,
            'avg_hidden_objects': avg_hidden_objects,
            'visibility_rate': avg_visible_objects/avg_scene_objects if avg_scene_objects > 0 else 0
        },
        'hiding_reasons': {
            'fallen_objects': fall_count,
            'out_of_view_objects': out_of_view_count
        },
        'detailed_results': results
    }
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n✅ 详细报告已保存: {report_path}")
    
    # 提供改进建议
    print(f"\n💡 改进建议:")
    if avg_visible_objects < avg_scene_objects * 0.7:
        print("   - 物体可见率较低，建议:")
        if fall_count > out_of_view_count:
            print("     • 增加物体初始高度")
            print("     • 减小物体掉落速度")
            print("     • 增加桌面摩擦力")
        else:
            print("     • 扩大相机视野范围")
            print("     • 调整相机角度")
            print("     • 减小工作空间范围")
    else:
        print("   - 物体可见率良好")

def main():
    """主函数"""
    print("🔍 SimplerEnv 物体可见性分析工具")
    print("=" * 40)
    
    results = analyze_object_positions_and_visibility()
    
    if results:
        print(f"\n🎉 分析完成！")
        print(f"📊 共分析了 {len(results)} 次试验")
        print(f"📁 结果保存在: /home/<USER>/claude/SpatialVLA/Z/Z_new_trial/")
    else:
        print("\n❌ 分析失败")

if __name__ == "__main__":
    main()
