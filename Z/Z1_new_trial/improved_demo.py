#!/usr/bin/env python3
"""
改进的SimplerEnv演示 - 明确显示多物体生成差异
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path

# 添加路径
sys.path.insert(0, '/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv')
sys.path.insert(0, '/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/ManiSkill2_real2sim')

def setup_environment():
    """设置环境"""
    try:
        import sapien.core as sapien
        sapien.render_config.rt_use_denoiser = False
        
        from mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env import DiverseEnhancedSceneEnv
        
        print("✅ 环境设置成功")
        return DiverseEnhancedSceneEnv
        
    except Exception as e:
        print(f"❌ 环境设置失败: {e}")
        return None

def create_comparison_demo():
    """创建对比演示"""
    print("🎬 创建SimplerEnv多物体对比演示")
    print("=" * 60)
    
    DiverseEnhancedSceneEnv = setup_environment()
    if DiverseEnhancedSceneEnv is None:
        return False
    
    # 对比配置：明显的差异
    configs = [
        {
            "name": "原始场景",
            "range": (2, 3),
            "description": "少量物体 (2-3个)",
            "color": "🔵"
        },
        {
            "name": "增强场景", 
            "range": (8, 12),
            "description": "大量物体 (8-12个)",
            "color": "🔴"
        }
    ]
    
    output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/comparison_demo")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    results = {}
    
    for config in configs:
        print(f"\n{config['color']} {config['name']}: {config['description']}")
        print("-" * 40)
        
        try:
            env = DiverseEnhancedSceneEnv(
                robot="google_robot_static",
                scene_name="dummy_tabletop",
                obs_mode="image",
                camera_cfgs={"add_segmentation": True},
                render_mode="cameras",
                num_objects_range=config['range'],
            )
            
            # 生成3个示例
            config_results = []
            for i in range(3):
                obs, info = env.reset()
                
                # 获取物体信息
                scene_config = info.get("scene_config", {})
                total_objects = scene_config.get("total_objects", 0)
                
                actual_objects = []
                if hasattr(env, 'all_scene_objects'):
                    actual_objects = [obj.name for obj in env.all_scene_objects]
                
                # 保存图像 (使用俯视相机以更好地显示所有物体)
                try:
                    camera_obs = obs['image']['overhead_camera']  # 使用俯视相机
                    image = camera_obs['Color']

                    # 处理图像格式 - 确保正确的数据范围
                    if image.shape[-1] == 4:  # RGBA
                        image = image[:, :, :3]  # 只取RGB

                    # 确保图像在正确范围内
                    if image.max() <= 1.0:
                        image_save = (image * 255).astype(np.uint8)
                    else:
                        image_save = image.astype(np.uint8)

                    # 在图像上添加物体计数信息
                    image_with_info = image_save.copy()
                    text = f"{config['name']}: {len(actual_objects)} objects"
                    cv2.putText(image_with_info, text, (10, 30),
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

                    image_path = output_dir / f"{config['name'].replace(' ', '_')}_example_{i+1}.png"
                    cv2.imwrite(str(image_path), cv2.cvtColor(image_with_info, cv2.COLOR_RGB2BGR))

                except Exception as e:
                    print(f"    图像保存失败: {e}")
                    import traceback
                    traceback.print_exc()
                    image_path = None
                
                result = {
                    "example_id": i + 1,
                    "configured_count": total_objects,
                    "actual_count": len(actual_objects),
                    "objects": actual_objects,
                    "image_path": str(image_path) if image_path else None
                }
                
                config_results.append(result)
                
                print(f"  示例 {i+1}: {len(actual_objects)}个物体")
                print(f"    物体列表: {', '.join(actual_objects[:3])}{'...' if len(actual_objects) > 3 else ''}")
                if image_path:
                    print(f"    图像: {image_path}")
            
            env.close()
            results[config['name']] = {
                "config": config,
                "examples": config_results
            }
            
            # 统计
            counts = [r["actual_count"] for r in config_results]
            print(f"  📊 统计: {min(counts)}-{max(counts)}个物体 (平均{np.mean(counts):.1f})")
            
        except Exception as e:
            print(f"    ❌ 失败: {e}")
            results[config['name']] = {"error": str(e)}
    
    # 生成对比报告
    print(f"\n📋 对比报告")
    print("=" * 60)
    
    if "原始场景" in results and "增强场景" in results:
        original = results["原始场景"]
        enhanced = results["增强场景"]
        
        if "error" not in original and "error" not in enhanced:
            orig_counts = [r["actual_count"] for r in original["examples"]]
            enh_counts = [r["actual_count"] for r in enhanced["examples"]]
            
            print(f"🔵 原始场景: {min(orig_counts)}-{max(orig_counts)}个物体")
            print(f"🔴 增强场景: {min(enh_counts)}-{max(enh_counts)}个物体")
            print(f"📈 增加倍数: {np.mean(enh_counts)/np.mean(orig_counts):.1f}x")
            print(f"➕ 平均增加: {np.mean(enh_counts) - np.mean(orig_counts):.1f}个物体")
            
            print(f"\n✅ 多物体生成功能验证成功！")
            print(f"📁 对比图像保存在: {output_dir}")
            
            return True
    
    print(f"❌ 对比演示失败")
    return False

def create_side_by_side_comparison():
    """创建并排对比图像"""
    print(f"\n🖼️  创建并排对比图像...")
    
    output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/comparison_demo")
    
    # 查找图像文件
    original_images = list(output_dir.glob("原始场景_example_*.png"))
    enhanced_images = list(output_dir.glob("增强场景_example_*.png"))
    
    if original_images and enhanced_images:
        # 创建并排对比
        for i in range(min(len(original_images), len(enhanced_images))):
            try:
                orig_img = cv2.imread(str(original_images[i]))
                enh_img = cv2.imread(str(enhanced_images[i]))
                
                if orig_img is not None and enh_img is not None:
                    # 调整图像大小使其一致
                    height = max(orig_img.shape[0], enh_img.shape[0])
                    orig_img = cv2.resize(orig_img, (int(orig_img.shape[1] * height / orig_img.shape[0]), height))
                    enh_img = cv2.resize(enh_img, (int(enh_img.shape[1] * height / enh_img.shape[0]), height))
                    
                    # 并排拼接
                    comparison = np.hstack([orig_img, enh_img])
                    
                    # 添加分割线
                    cv2.line(comparison, (orig_img.shape[1], 0), (orig_img.shape[1], height), (255, 255, 255), 3)
                    
                    # 保存对比图像
                    comparison_path = output_dir / f"comparison_{i+1}.png"
                    cv2.imwrite(str(comparison_path), comparison)
                    print(f"  📸 对比图像 {i+1}: {comparison_path}")
                    
            except Exception as e:
                print(f"  ❌ 对比图像 {i+1} 创建失败: {e}")
        
        print(f"✅ 并排对比图像创建完成")
    else:
        print(f"❌ 未找到足够的图像文件进行对比")

if __name__ == "__main__":
    print("🚀 SimplerEnv 改进演示")
    print("=" * 60)
    
    success = create_comparison_demo()
    
    if success:
        create_side_by_side_comparison()
        print(f"\n🎉 演示完成！现在可以清楚地看到多物体生成的差异")
    else:
        print(f"\n⚠️  演示失败，请检查环境配置")
    
    print("\n" + "=" * 60)
