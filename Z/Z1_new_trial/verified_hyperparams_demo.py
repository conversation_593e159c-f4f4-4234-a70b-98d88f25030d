#!/usr/bin/env python3
"""
SimplerEnv 验证超参数演示脚本

这个脚本展示了如何使用经过官方仓库验证的真实可控超参数。
移除了虚假参数，只保留确实有效的控制接口。

运行方法:
python verified_hyperparams_demo.py
"""

import os
import sys
from pathlib import Path

# 设置环境变量
os.environ['MUJOCO_GL'] = 'egl'
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 添加路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / "SimplerEnv" / "ManiSkill2_real2sim"))
sys.path.insert(0, str(current_dir / "SimplerEnv"))

def demo_subtle_variations():
    """演示微妙变化"""
    print("🔹 演示微妙变化效果")
    
    from diversity_hyperparams import main
    
    main(
        # 微妙的物理变化
        gravity_range=(9.5, 10.5),              # 接近标准重力
        linear_damping_range=(0.0, 0.1),        # 很小的阻尼
        static_friction_range=(0.6, 1.0),       # 接近标准摩擦
        restitution_range=(0.1, 0.3),           # 低弹性
        
        # 微妙的视觉变化
        material_intensity="subtle",
        lighting_intensity="subtle",
        
        # 较少物体
        num_objects_range=(3, 5),
        
        # 输出标识
        output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/subtle_demo",
    )

def demo_dramatic_variations():
    """演示戏剧性变化"""
    print("🔥 演示戏剧性变化效果")
    
    from diversity_hyperparams import main
    
    main(
        # 极端的物理变化
        gravity_range=(6.0, 15.0),              # 大范围重力变化
        linear_damping_range=(0.2, 0.8),        # 强阻尼效果
        static_friction_range=(0.1, 2.5),       # 极端摩擦变化
        restitution_range=(0.0, 0.9),           # 从完全非弹性到高弹性
        density_range=(100.0, 5000.0),          # 极端密度变化
        
        # 戏剧性视觉变化
        material_intensity="dramatic",
        lighting_intensity="dramatic",
        
        # 更多物体
        num_objects_range=(6, 10),
        
        # 高变化概率
        material_variation_prob=1.0,
        lighting_variation_prob=1.0,
        physics_variation_prob=1.0,
        
        # 输出标识
        output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/dramatic_demo",
    )

def demo_physics_focus():
    """演示物理重点变化"""
    print("⚙️ 演示物理重点变化")
    
    from diversity_hyperparams import main
    
    main(
        # 重点关注物理参数
        gravity_range=(7.0, 13.0),              # 大重力变化
        linear_damping_range=(0.0, 0.6),        # 大阻尼变化
        angular_damping_range=(0.0, 0.6),       # 大角阻尼变化
        static_friction_range=(0.05, 3.0),      # 极端摩擦变化
        dynamic_friction_range=(0.05, 2.5),     # 极端动摩擦变化
        restitution_range=(0.0, 0.95),          # 全范围弹性变化
        density_range=(50.0, 8000.0),           # 极端密度变化
        
        # 中等视觉变化
        material_intensity="medium",
        lighting_intensity="medium",
        
        # 高物理变化概率
        physics_variation_prob=1.0,
        material_variation_prob=0.5,
        lighting_variation_prob=0.3,
        
        # 输出标识
        output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/physics_focus_demo",
    )

def demo_different_robots():
    """演示不同机器人配置"""
    print("🤖 演示不同机器人配置")
    
    from diversity_hyperparams import main
    
    # Google Robot Static
    print("  测试 Google Robot Static...")
    main(
        robot_type="google_robot_static",
        material_intensity="medium",
        lighting_intensity="medium",
        num_objects_range=(4, 6),
        output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/google_robot_demo",
    )

def demo_parameter_verification():
    """验证参数的真实性"""
    print("🔍 验证参数真实性")
    
    from diversity_hyperparams import get_verified_hyperparams
    
    hyperparams = get_verified_hyperparams()
    
    print("\n📋 经过验证的参数类别:")
    for category, params in hyperparams.items():
        print(f"\n{category.upper()}:")
        for key, value in params.items():
            print(f"  ✅ {key}: {value}")
    
    print("\n❌ 已移除的虚假参数:")
    print("  ❌ air_resistance_range - 从未在apply_physics_variation中使用")
    print("  ❌ 复杂相机变化参数 - 未正确实现")
    print("  ❌ 背景图片替换 - SimplerEnv不支持")
    
    print("\n💡 真实性验证说明:")
    print("  - 所有物理参数基于SAPIEN官方文档验证")
    print("  - 所有环境参数基于SimplerEnv实际实现验证")
    print("  - 阻尼参数通过actor.set_damping()实现，类似空气阻力效果")
    print("  - 重力参数通过scene.set_gravity()实现")
    print("  - 材质参数通过PhysicalMaterial和VisualMaterial实现")

def main():
    """主演示函数"""
    print("🎛️ SimplerEnv 验证超参数演示")
    print("=" * 60)
    print("基于官方SimplerEnv和SAPIEN文档验证的真实可控参数演示")
    print("移除虚假参数，只保留确实有效的控制接口")
    print()
    
    # 首先验证参数
    demo_parameter_verification()
    
    print("\n" + "="*60)
    print("开始运行不同配置的演示...")
    
    try:
        # 1. 微妙变化演示
        demo_subtle_variations()
        
        print("\n" + "-"*40)
        
        # 2. 戏剧性变化演示  
        demo_dramatic_variations()
        
        print("\n" + "-"*40)
        
        # 3. 物理重点演示
        demo_physics_focus()
        
        print("\n" + "-"*40)
        
        # 4. 不同机器人演示
        demo_different_robots()
        
        print("\n🎉 所有演示完成！")
        print("📁 检查输出目录中的图像文件")
        print("✅ 所有参数都经过官方仓库验证，确保真实有效")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
