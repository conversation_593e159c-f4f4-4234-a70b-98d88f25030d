# SimplerEnv 完整可修改参数列表

## 📋 你的总结对比

你提到的可修改内容：
1. ✅ **各物品初始位置、朝向** - 正确
2. ✅ **各物品种类数量** - 正确  
3. ✅ **光照方向** - 正确
4. ❌ **背景图片** - 当前系统中没有这个功能
5. ✅ **机械臂的种类和所处位置** - 正确

## 🔍 完整的可修改参数列表

### 1. 物体相关参数 ✅

#### 物体数量和类型
```python
ENVIRONMENT_CONFIG = {
    "num_objects_range": (4, 8),  # 物体数量范围
}

# 物体类型（在custom_scene_config.py中）
class ObjectType(Enum):
    CAN = "can"           # 罐子
    BOTTLE = "bottle"     # 瓶子  
    CUBE = "cube"         # 方块
    FRUIT = "fruit"       # 水果
    TOOL = "tool"         # 工具
    CONTAINER = "container"  # 容器
```

#### 物体位置和朝向
- 物体位置通过场景配置自动随机生成
- 可以通过修改场景配置来控制位置分布范围

### 2. 材质变化参数 ✅ (你遗漏的重要部分)

```python
MATERIAL_HYPERPARAMS = {
    # 物理属性
    "friction_range": [0.05, 5.0],        # 摩擦力
    "restitution_range": [0.0, 0.95],     # 弹性系数
    "density_range": [0.1, 5.0],          # 密度
    
    # 视觉属性
    "metallic_range": [0.0, 1.0],         # 金属度
    "roughness_range": [0.0, 1.0],        # 粗糙度
    "color_variations": [                  # 颜色变化
        (3.0, 0.2, 0.2),    # 超鲜红
        (0.2, 3.0, 0.2),    # 超鲜绿
        # ... 更多颜色
    ]
}
```

### 3. 光照参数 ✅

#### 光照强度和方向
```python
LIGHTING_HYPERPARAMS = {
    # 环境光
    "ambient_range": [(0.0, 0.0, 0.0), (1.0, 1.0, 1.0)],
    
    # 方向光
    "directional_intensity_range": [0.1, 8.0],
    "directional_color_range": [(0.3, 0.3, 0.3), (3.0, 3.0, 3.0)],
    
    # 特殊效果
    "shadow_probability": 1.0,
    "colored_light_probability": 0.6
}
```

### 4. 机器人配置 ✅

```python
ENVIRONMENT_CONFIG = {
    "robot": "google_robot_static",  # 可选: google_robot_static, google_robot_mobile
}

# 可用的机器人类型：
# - GoogleRobotStaticBase: 固定底座
# - GoogleRobotMobileBase: 移动底座
```

### 5. 相机配置 ✅ (你遗漏的重要部分)

```python
ENVIRONMENT_CONFIG = {
    "camera_cfgs": {"add_segmentation": True},
    "obs_mode": "image",
    "render_mode": "cameras",
}

# 相机详细配置（在CameraConfig中）:
# - 位置 (p): [x, y, z]
# - 朝向 (q): [qw, qx, qy, qz] 
# - 分辨率: width, height
# - 视野角度: fov
# - 近远平面: near, far
```

### 6. 物理环境参数 ✅ (你遗漏的重要部分)

```python
# 在diversity_enhancer.py中
self.physics_params = {
    "gravity_range": [8.0, 12.0],           # 重力变化
    "air_resistance_range": [0.0, 0.1],     # 空气阻力
    "contact_offset_range": [0.001, 0.01]   # 接触偏移
}
```

### 7. 场景配置 ✅

```python
ENVIRONMENT_CONFIG = {
    "scene_name": "dummy_tabletop",    # 场景类型
    "diversity_level": "medium",       # 多样性级别
}
```

### 8. 变化概率控制 ✅ (你遗漏的重要部分)

```python
ENVIRONMENT_CONFIG = {
    "lighting_variation_prob": 1.0,    # 光照变化概率
    "material_variation_prob": 1.0,    # 材质变化概率  
    "physics_variation_prob": 0.8      # 物理变化概率
}
```

## ❌ 你提到但当前系统没有的功能

### 背景图片
- 当前系统没有背景图片更换功能
- 背景是通过场景几何体和光照渲染生成的
- 如果需要可以添加天空盒或背景纹理功能

## 📊 完整参数分类总结

| 类别 | 你提到的 | 实际存在的 | 你遗漏的重要参数 |
|------|----------|------------|------------------|
| **物体** | ✅ 位置、朝向、种类、数量 | ✅ | 材质属性、物理属性 |
| **光照** | ✅ 方向 | ✅ | 强度、颜色、阴影、特效 |
| **机器人** | ✅ 种类、位置 | ✅ | 控制模式 |
| **相机** | ❌ 未提到 | ✅ | 位置、朝向、参数 |
| **物理** | ❌ 未提到 | ✅ | 重力、摩擦、弹性 |
| **概率** | ❌ 未提到 | ✅ | 各种变化的概率 |
| **背景** | ❌ 不存在 | ❌ | - |

## 🎯 修正后的完整列表

你可以修改的参数包括：

1. **物体参数**
   - ✅ 位置、朝向（通过场景配置）
   - ✅ 种类、数量
   - ➕ 材质属性（颜色、金属度、粗糙度）
   - ➕ 物理属性（摩擦力、弹性、密度）

2. **光照参数**
   - ✅ 方向
   - ➕ 强度、颜色
   - ➕ 环境光、阴影
   - ➕ 特殊效果（彩色光照等）

3. **机器人配置**
   - ✅ 种类（静态/移动底座）
   - ✅ 位置（通过场景配置）
   - ➕ 控制模式

4. **相机配置** (新增)
   - ➕ 位置、朝向
   - ➕ 分辨率、视野角度
   - ➕ 分割、深度等功能

5. **物理环境** (新增)
   - ➕ 重力大小
   - ➕ 空气阻力
   - ➕ 接触参数

6. **概率控制** (新增)
   - ➕ 各种变化的应用概率

7. **背景图片** ❌
   - 当前系统不支持

## 💡 总结

你的总结基本正确，但**遗漏了很多重要的可配置参数**，特别是：
- 材质和物理属性的详细控制
- 相机配置参数
- 光照的详细控制（不只是方向）
- 各种变化的概率控制

这些参数对于创建多样化的训练数据非常重要！
