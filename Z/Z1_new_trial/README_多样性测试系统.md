# SimplerEnv 多样性测试系统

这是一个完整的SimplerEnv多样性测试系统，提供了多种方式来生成和测试不同强度的多样性场景。

## 🎯 系统功能

### 1. 详细的多样性信息输出
- **物体信息**：显示场景中所有物体的名称和数量
- **光照详情**：光源类型、方向、颜色、强度等
- **材质变化**：每个物体的颜色、摩擦力、弹性、密度变化
- **物理属性**：重力、时间步长等物理参数
- **统计摘要**：总变化数量、受影响物体数等

### 2. 图片文件路径输出
每次生成都会输出完整的图片文件路径，格式如：
```
/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/web_test_dramatic_subtle_20250707_214409.png
```

## 🛠️ 使用方式

### 方式1：命令行工具

#### 快速测试工具
```bash
# 测试不同强度组合
python quick_diversity_test.py dramatic dramatic  # 极强材质 + 极强光照
python quick_diversity_test.py dramatic subtle    # 极强材质 + 微弱光照  
python quick_diversity_test.py subtle dramatic    # 微弱材质 + 极强光照
python quick_diversity_test.py medium medium      # 中等材质 + 中等光照
```

#### 超参数配置工具
```bash
# 使用预设配置
python diversity_hyperparams.py
```

### 方式2：Web界面（推荐）

#### 启动服务器
```bash
python diversity_server.py
```

#### 访问界面
- **简化界面**：http://localhost:5000
- **高级界面**：http://localhost:5000/advanced

### Web界面功能
- 🎨 **9种强度组合**：3×3矩阵覆盖所有材质×光照组合
- 🚀 **一键批量生成**：自动生成所有9种组合
- 📋 **一键复制路径**：点击即可复制图片文件路径
- 🖼️ **直接打开图片**：点击链接直接查看生成的图片
- 📊 **详细信息显示**：每个测试的完整多样性信息

## 📁 文件结构

```
Z/Z1_new_trial/
├── diversity_hyperparams.py          # 超参数配置工具
├── quick_diversity_test.py           # 快速命令行测试
├── diversity_server.py               # Web服务器
├── simple_diversity_interface.html   # 简化Web界面
├── diversity_interactive.html        # 高级Web界面
├── SimplerEnv/                       # SimplerEnv环境代码
│   ├── diversity_enhancer.py         # 多样性增强器
│   └── ManiSkill2_real2sim/          # 环境实现
└── 生成的图片文件...
```

## 🎛️ 强度级别说明

### 材质强度
- **Subtle（微弱）**：轻微的颜色和物理属性变化
- **Medium（中等）**：中等程度的材质变化
- **Dramatic（极强）**：极大的颜色对比和物理属性变化

### 光照强度
- **Subtle（微弱）**：轻微的环境光变化
- **Medium（中等）**：中等的光照变化
- **Dramatic（极强）**：强烈的彩色光照和阴影效果

## 📊 输出示例

### 详细信息输出
```
📊 详细多样性变化报告:
============================================================
🎯 场景物体总数: 8
   物体列表: arena, orange_target, sprite_can_distractor, apple_distractor, ...

🔆 光照系统变化:
   类型: colored
   光源数量: 2
   光源 1:
     - 类型: colored_main
     - 方向: (0.00, 0.00, -1.00)
     - 颜色: RGB(2.00, 1.50, 1.00)

🎨 材质变化详情 (7 个物体):
   📦 物体: orange_target
     - 颜色变化: RGB(0.95, 0.95, 1.00)
       金属度: 0.16
       粗糙度: 0.39
     - 摩擦力变化:
       倍数: 1.28
       静摩擦: 1.541
       动摩擦: 1.156

📈 变化统计摘要:
   总变化数量: 11
   受影响物体: 7
   光照变化: 是
   物理变化: 否
```

### 图片路径输出
```
🖼️ 图片文件路径:
/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/web_test_subtle_dramatic_20250707_212955.png
```

## 🔧 技术特性

### 多样性增强功能
- ✅ **光照变化**：环境光、方向光、彩色光照
- ✅ **材质变化**：颜色、摩擦力、弹性、密度、金属度、粗糙度
- ✅ **物理变化**：重力、接触参数
- ✅ **随机种子控制**：可重现的结果

### Web界面特性
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **实时状态更新**：显示生成进度
- ✅ **错误处理**：友好的错误信息显示
- ✅ **批量操作**：支持一次生成多个组合

## 🚀 快速开始

1. **启动Web服务器**
   ```bash
   cd /home/<USER>/claude/SpatialVLA/Z/Z1_new_trial
   python diversity_server.py
   ```

2. **打开浏览器访问**
   ```
   http://localhost:5000
   ```

3. **选择强度组合并生成**
   - 点击任意强度组合按钮
   - 或点击"生成所有9种组合"批量生成

4. **获取图片路径**
   - 生成完成后，点击"复制路径"按钮
   - 或点击"打开图片"直接查看

## 📝 注意事项

- 确保CUDA环境正确配置
- 生成过程可能需要几秒到几十秒时间
- 图片保存在 `/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/` 目录
- Web界面支持同时运行多个测试任务
- 所有生成的图片都包含时间戳，避免文件名冲突

## 🎉 总结

这个系统提供了完整的SimplerEnv多样性测试解决方案，从命令行工具到Web界面，从单次测试到批量生成，满足不同使用场景的需求。特别是详细的输出信息和图片路径链接功能，让用户能够清楚地了解每个生成场景的具体变化情况。
