#!/usr/bin/env python3
"""
SimplerEnv 增强多样性演示脚本

这个脚本专门用于演示和测试材质变化和光照变化的效果，
提供可配置的超参数接口，并保存对比图像。

功能：
- 可配置的材质和光照变化强度
- 保存原始场景和变化后场景的对比图像
- 详细的变化信息输出
- 超参数接口
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path
import json
from datetime import datetime
import argparse
from typing import Dict, List, Optional, Tuple, Any

# 设置环境变量
os.environ['MUJOCO_GL'] = 'egl'
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 添加路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / "SimplerEnv" / "ManiSkill2_real2sim"))
sys.path.insert(0, str(current_dir / "SimplerEnv"))

# 输出目录配置
OUTPUT_BASE_DIR = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")


class EnhancedDiversityDemo:
    """增强多样性演示类"""
    
    def __init__(self, output_dir: Optional[Path] = None):
        """初始化演示类"""
        self.output_dir = output_dir or OUTPUT_BASE_DIR
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        self.comparison_dir = self.output_dir / "diversity_comparison"
        self.reports_dir = self.output_dir / "diversity_reports"
        
        for dir_path in [self.comparison_dir, self.reports_dir]:
            dir_path.mkdir(exist_ok=True)
        
        print(f"📁 增强多样性演示输出目录: {self.output_dir}")
        print(f"   - 对比图像目录: {self.comparison_dir}")
        print(f"   - 报告目录: {self.reports_dir}")
    
    def setup_environment(self):
        """设置SimplerEnv环境"""
        print("🔧 设置SimplerEnv环境...")
        
        try:
            # 导入必要模块
            import gymnasium as gym
            import simpler_env
            import sapien.core as sapien
            
            # 关闭降噪
            sapien.render_config.rt_use_denoiser = False
            
            # 导入自定义环境
            from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes import enhanced_diverse_env
            
            print("✅ SimplerEnv环境设置完成")
            return True
            
        except Exception as e:
            print(f"❌ 环境设置失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_image_from_obs(self, env, obs) -> np.ndarray:
        """从观察中提取图像"""
        try:
            # 尝试不同的图像键
            if 'image' in obs and 'overhead_camera' in obs['image']:
                camera_obs = obs['image']['overhead_camera']
                if 'Color' in camera_obs:
                    return camera_obs['Color']
                elif 'rgb' in camera_obs:
                    return camera_obs['rgb']
                elif 'color' in camera_obs:
                    return camera_obs['color']
            
            # 如果上述都不行，尝试其他相机
            if 'image' in obs:
                for camera_name, camera_data in obs['image'].items():
                    if isinstance(camera_data, dict):
                        for key in ['Color', 'rgb', 'color']:
                            if key in camera_data:
                                return camera_data[key]
            
            print("⚠ 无法找到图像数据，返回空图像")
            return np.zeros((480, 640, 3), dtype=np.uint8)
            
        except Exception as e:
            print(f"⚠ 图像提取失败: {e}")
            return np.zeros((480, 640, 3), dtype=np.uint8)
    
    def save_comparison_image(self, original_image: np.ndarray, 
                            enhanced_image: np.ndarray,
                            filename: str,
                            diversity_info: Dict) -> Path:
        """保存对比图像"""
        try:
            # 确保图像是正确的格式
            if original_image.dtype == np.float32 or original_image.dtype == np.float64:
                original_image = (original_image * 255).astype(np.uint8)
            if enhanced_image.dtype == np.float32 or enhanced_image.dtype == np.float64:
                enhanced_image = (enhanced_image * 255).astype(np.uint8)
            
            # 创建对比图像
            height, width = original_image.shape[:2]
            comparison_image = np.zeros((height, width * 2 + 20, 3), dtype=np.uint8)
            
            # 放置原始图像
            comparison_image[:, :width] = cv2.cvtColor(original_image[:,:,:3], cv2.COLOR_RGB2BGR)
            
            # 放置增强图像
            comparison_image[:, width + 20:] = cv2.cvtColor(enhanced_image[:,:,:3], cv2.COLOR_RGB2BGR)
            
            # 添加分隔线
            comparison_image[:, width:width+20] = [255, 255, 255]
            
            # 添加文字标签
            cv2.putText(comparison_image, "Original", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(comparison_image, "Enhanced", (width + 30, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # 添加多样性信息
            y_offset = 60
            if 'lighting' in diversity_info:
                lighting_type = diversity_info['lighting'].get('type', 'unknown')
                cv2.putText(comparison_image, f"Lighting: {lighting_type}", 
                           (width + 30, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 1)
                y_offset += 25
            
            if 'materials' in diversity_info:
                material_count = len(diversity_info['materials'])
                cv2.putText(comparison_image, f"Materials: {material_count} objects", 
                           (width + 30, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 1)
            
            # 保存图像
            image_path = self.comparison_dir / filename
            cv2.imwrite(str(image_path), comparison_image)
            print(f"✅ 对比图像已保存: {image_path}")
            return image_path
            
        except Exception as e:
            print(f"❌ 对比图像保存失败: {e}")
            return None
    
    def demo_diversity_levels(self, material_intensity: str = "medium", 
                            lighting_intensity: str = "medium",
                            custom_params: Optional[Dict] = None) -> Dict[str, Any]:
        """演示不同强度的多样性变化"""
        print(f"\n🌟 演示多样性变化")
        print(f"材质强度: {material_intensity}, 光照强度: {lighting_intensity}")
        print("=" * 60)
        
        try:
            from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes import enhanced_diverse_env
            from diversity_enhancer import DiversityEnhancer
            
            # 创建自定义多样性增强器
            enhancer = DiversityEnhancer(
                seed=42,
                material_intensity=material_intensity,
                lighting_intensity=lighting_intensity,
                custom_params=custom_params
            )
            
            # 创建环境（先不应用变化）- 修改为10个物体
            # 注意：不能使用预定义的子类，因为它们会覆盖num_objects_range
            # 直接使用基类EnhancedDiverseSceneEnv
            env = enhanced_diverse_env.EnhancedDiverseSceneEnv(
                robot="google_robot_static",
                scene_name="dummy_tabletop",
                obs_mode="image",
                camera_cfgs={"add_segmentation": True},
                render_mode="cameras",
                num_objects_range=(10, 10),  # 修改为固定10个物体
                diversity_level="custom",    # 使用custom避免子类覆盖
                lighting_variation_prob=0.0,  # 先不应用变化
                material_variation_prob=0.0,
                physics_variation_prob=0.0
            )
            
            print("✅ 环境创建成功")
            
            # 获取原始场景
            obs_original, _ = env.reset()
            original_image = self.get_image_from_obs(env, obs_original)
            print("✅ 原始场景获取成功")
            
            # 手动应用多样性变化
            env.diversity_enhancer = enhancer
            env.lighting_variation_prob = 1.0
            env.material_variation_prob = 1.0
            
            # 重置以应用变化
            obs_enhanced, _ = env.reset()
            enhanced_image = self.get_image_from_obs(env, obs_enhanced)
            print("✅ 增强场景获取成功")
            
            # 获取多样性信息
            diversity_info = getattr(env, 'current_diversity_info', {})
            
            # 保存对比图像
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"diversity_comparison_{material_intensity}_{lighting_intensity}_{timestamp}.png"
            comparison_path = self.save_comparison_image(
                original_image, enhanced_image, filename, diversity_info
            )
            
            # 详细输出变化信息
            self._print_diversity_details(diversity_info)
            
            env.close()
            
            result = {
                "material_intensity": material_intensity,
                "lighting_intensity": lighting_intensity,
                "comparison_image": str(comparison_path) if comparison_path else None,
                "diversity_info": diversity_info,
                "timestamp": timestamp
            }
            
            print("✅ 多样性演示完成")
            return result
            
        except Exception as e:
            print(f"❌ 多样性演示失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}
    
    def _print_diversity_details(self, diversity_info: Dict):
        """打印详细的多样性信息"""
        print("\n📊 详细多样性变化信息:")
        print("-" * 40)
        
        # 光照变化信息
        if 'lighting' in diversity_info:
            lighting = diversity_info['lighting']
            print(f"🔆 光照变化:")
            print(f"   类型: {lighting.get('type', '未知')}")
            if 'ambient' in lighting:
                ambient = lighting['ambient']
                print(f"   环境光: RGB({ambient[0]:.2f}, {ambient[1]:.2f}, {ambient[2]:.2f})")
            if 'lights' in lighting:
                print(f"   光源数量: {len(lighting['lights'])}")
                for i, light in enumerate(lighting['lights'][:3]):  # 只显示前3个
                    light_type = light.get('type', '未知')
                    print(f"     光源{i+1}: {light_type}")
        
        # 材质变化信息
        if 'materials' in diversity_info:
            materials = diversity_info['materials']
            print(f"\n🎨 材质变化 ({len(materials)} 个物体):")
            for i, mat_info in enumerate(materials):
                obj_name = mat_info.get('object_name', '未知物体')
                variations = mat_info.get('variations', [])
                print(f"   物体{i+1}: {obj_name}")
                for var in variations:
                    var_type = var.get('type', '未知')
                    if var_type == 'color':
                        color = var.get('color', [1, 1, 1])
                        metallic = var.get('metallic', 0)
                        roughness = var.get('roughness', 0.5)
                        applied_shapes = var.get('applied_shapes', 0)
                        print(f"     - 颜色: RGB({color[0]:.2f}, {color[1]:.2f}, {color[2]:.2f})")
                        print(f"     - 金属度: {metallic:.2f}, 粗糙度: {roughness:.2f}")
                        print(f"     - 应用到 {applied_shapes} 个形状")
                    elif var_type == 'friction':
                        friction = var.get('multiplier', 1.0)
                        print(f"     - 摩擦力倍数: {friction:.2f}")
                    elif var_type == 'restitution':
                        restitution = var.get('value', 0.0)
                        print(f"     - 弹性系数: {restitution:.2f}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SimplerEnv 增强多样性演示")
    parser.add_argument("--material-intensity", choices=["subtle", "medium", "dramatic"],
                       default="medium", help="材质变化强度")
    parser.add_argument("--lighting-intensity", choices=["subtle", "medium", "dramatic"],
                       default="medium", help="光照变化强度")
    parser.add_argument("--output-dir", type=str, default=None, help="输出目录路径")
    parser.add_argument("--demo-all", action="store_true", help="演示所有强度组合")

    # 自定义参数
    parser.add_argument("--custom-friction-range", nargs=2, type=float,
                       help="自定义摩擦力范围 (min max)")
    parser.add_argument("--custom-color-intensity", type=float, default=None,
                       help="自定义颜色强度倍数")
    parser.add_argument("--custom-lighting-intensity", type=float, default=None,
                       help="自定义光照强度倍数")

    args = parser.parse_args()

    # 创建演示实例
    output_dir = Path(args.output_dir) if args.output_dir else OUTPUT_BASE_DIR
    demo = EnhancedDiversityDemo(output_dir)

    # 设置环境
    if not demo.setup_environment():
        print("❌ 环境设置失败")
        return 1

    # 构建自定义参数
    custom_params = {}
    if args.custom_friction_range:
        custom_params["material"] = {"friction_range": args.custom_friction_range}

    if args.custom_color_intensity:
        if "material" not in custom_params:
            custom_params["material"] = {}
        # 调整颜色变化强度
        base_colors = [
            (1.0, 0.6, 0.6), (0.6, 1.0, 0.6), (0.6, 0.6, 1.0),
            (1.0, 1.0, 0.6), (1.0, 0.6, 1.0), (0.6, 1.0, 1.0)
        ]
        intensity = args.custom_color_intensity
        custom_colors = [(r*intensity, g*intensity, b*intensity) for r, g, b in base_colors]
        custom_params["material"]["color_variations"] = custom_colors

    if args.custom_lighting_intensity:
        if "lighting" not in custom_params:
            custom_params["lighting"] = {}
        intensity = args.custom_lighting_intensity
        custom_params["lighting"]["directional_intensity_range"] = [0.3*intensity, 3.0*intensity]

    try:
        results = {}

        if args.demo_all:
            print("🚀 演示所有强度组合")
            intensities = ["subtle", "medium", "dramatic"]

            for mat_intensity in intensities:
                for light_intensity in intensities:
                    print(f"\n{'='*60}")
                    result = demo.demo_diversity_levels(
                        material_intensity=mat_intensity,
                        lighting_intensity=light_intensity,
                        custom_params=custom_params if custom_params else None
                    )
                    results[f"{mat_intensity}_{light_intensity}"] = result
        else:
            print(f"🎯 演示单一配置: 材质={args.material_intensity}, 光照={args.lighting_intensity}")
            result = demo.demo_diversity_levels(
                material_intensity=args.material_intensity,
                lighting_intensity=args.lighting_intensity,
                custom_params=custom_params if custom_params else None
            )
            results["single_demo"] = result

        # 保存结果报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = demo.reports_dir / f"diversity_demo_report_{timestamp}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)

        print(f"\n🎉 演示完成！")
        print(f"📁 输出目录: {demo.output_dir}")
        print(f"📊 详细报告: {report_path}")

        # 显示生成的对比图像
        comparison_files = list(demo.comparison_dir.glob("*.png"))
        if comparison_files:
            print(f"\n📸 生成的对比图像 ({len(comparison_files)} 个):")
            for img_file in sorted(comparison_files):
                rel_path = img_file.relative_to(demo.output_dir)
                print(f"   - {rel_path}")

        return 0

    except KeyboardInterrupt:
        print("\n⚠ 用户中断执行")
        return 1
    except Exception as e:
        print(f"\n❌ 执行过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
