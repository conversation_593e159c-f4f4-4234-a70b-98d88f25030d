# SimplerEnv 自定义多样化场景配置指南

本文档详细说明了如何使用新创建的自定义场景参数系统来增强训练数据的多样性。

## 📋 目录

- [概述](#概述)
- [环境列表](#环境列表)
- [快速开始](#快速开始)
- [配置系统](#配置系统)
- [多样性增强功能](#多样性增强功能)
- [测试和验证](#测试和验证)
- [高级用法](#高级用法)
- [故障排除](#故障排除)

## 🎯 概述

本项目在SimplerEnv基础上实现了一套完整的自定义场景参数系统，支持：

- **物体多样性**: 不同类型、大小、颜色的物体
- **场景布局**: 可配置的物体数量、位置、空间分布
- **光照变化**: 多种光照模式和强度
- **材质属性**: 摩擦力、弹性、密度等物理属性变化
- **训练数据生成**: 自动生成大量多样化的训练场景

## 🌟 环境列表

### 基础多样化环境
- `custom_diverse_pick_scene`: 基础多样化拾取场景
- `custom_enhanced_scene`: 增强版多样化场景
- `custom_cluttered_scene`: 杂乱多样化场景

### 分级多样性环境
- `custom_enhanced_low_diversity`: 低多样性增强场景
- `custom_enhanced_medium_diversity`: 中等多样性增强场景
- `custom_enhanced_high_diversity`: 高多样性增强场景
- `custom_enhanced_extreme_diversity`: 极高多样性增强场景

### 专用环境
- `custom_training_data_generation`: 专门用于训练数据生成的场景

## 🚀 快速开始

### 1. 环境设置

```bash
# 进入实验目录
cd /home/<USER>/claude/SpatialVLA/Z/Z1_new_trial

# 运行快速测试
python quick_test.py
```

### 2. 基本使用

```python
import sys
sys.path.append("SimplerEnv")
import simpler_env

# 创建基础多样化环境
env = simpler_env.make("custom_diverse_pick_scene")

# 重置环境
obs, info = env.reset(seed=42)

# 获取语言指令
instruction = env.get_language_instruction()
print(f"任务指令: {instruction}")

# 执行动作
action = env.action_space.sample()
obs, reward, terminated, truncated, info = env.step(action)

env.close()
```

### 3. 使用不同多样性级别

```python
# 低多样性 - 适合初始训练
env_low = simpler_env.make("custom_enhanced_low_diversity")

# 高多样性 - 适合鲁棒性训练
env_high = simpler_env.make("custom_enhanced_high_diversity")

# 极高多样性 - 适合泛化能力训练
env_extreme = simpler_env.make("custom_enhanced_extreme_diversity")
```

## ⚙️ 配置系统

### 场景配置类

```python
from custom_scene_config import CustomSceneConfig, ObjectConfig, LightingConfig

# 创建自定义场景配置
config = CustomSceneConfig(
    scene_name="my_custom_scene",
    target_objects=[
        ObjectConfig(
            object_type="cube",
            model_id="green_cube_3cm",
            scale=1.2,
            density=800
        )
    ],
    distractor_objects=[
        ObjectConfig(
            object_type="can",
            model_id="coke_can",
            scale=1.0
        )
    ],
    num_target_objects=(1, 2),
    num_distractor_objects=(2, 4)
)
```

### 配置生成器

```python
from custom_scene_config import SceneConfigGenerator

# 创建配置生成器
generator = SceneConfigGenerator()

# 生成随机配置
random_config = generator.generate_random_config(
    num_objects_range=(3, 8),
    include_distractors=True,
    seed=123
)

# 生成训练配置批次
training_configs = generator.create_diverse_training_configs(
    num_configs=100,
    seed=42
)
```

## 🎨 多样性增强功能

### 光照变化

支持多种光照模式：
- `DEFAULT`: 标准光照
- `BRIGHT`: 明亮光照
- `DIM`: 昏暗光照
- `DRAMATIC`: 戏剧性光照
- `SOFT`: 柔和光照
- `COLORED`: 彩色光照

### 材质属性变化

- **摩擦力变化**: 0.1 - 2.0倍
- **弹性变化**: 0.0 - 0.8
- **密度变化**: 0.3 - 3.0倍
- **颜色变化**: 多种色调变体

### 物理属性变化

- **重力变化**: 8.0 - 12.0 m/s²
- **接触参数**: 动态调整
- **空气阻力**: 可选配置

## 🧪 测试和验证

### 快速测试

```bash
# 运行快速测试验证环境配置
python quick_test.py
```

### 完整测试

```bash
# 运行完整测试套件
python test_custom_environments.py
```

测试将生成：
- 视频文件展示环境运行
- JSON结果文件记录测试数据
- 多样性配置示例

### 测试输出结构

```
test_outputs/
├── custom_diverse_pick_scene/
│   ├── episode_1_seed_1234.mp4
│   ├── episode_2_seed_5678.mp4
│   └── test_results.json
├── custom_enhanced_high_diversity/
│   └── ...
└── test_summary.json
```

## 🔧 高级用法

### 自定义环境创建

```python
from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes.enhanced_diverse_env import EnhancedDiverseSceneEnv

# 创建自定义环境实例
env = EnhancedDiverseSceneEnv(
    diversity_level="high",
    lighting_variation_prob=0.9,
    material_variation_prob=0.8,
    physics_variation_prob=0.6,
    num_objects_range=(4, 8)
)
```

### 训练数据批量生成

```python
# 使用专用环境生成训练数据
env = simpler_env.make("custom_training_data_generation")

# 生成训练批次
batch_data = env.generate_training_batch(
    batch_size=1000,
    base_seed=42
)

# 保存训练配置
import json
with open("training_batch.json", "w") as f:
    json.dump(batch_data, f, indent=2)
```

### 多样性信息获取

```python
# 重置环境并获取多样性信息
obs, info = env.reset(seed=42)

# 查看应用的多样性变化
diversity_info = info.get("diversity_info", {})
print(f"光照类型: {diversity_info.get('lighting', {}).get('type')}")
print(f"材质变化: {diversity_info.get('materials', [])}")

# 获取多样性摘要
if hasattr(env, 'get_diversity_summary'):
    summary = env.get_diversity_summary()
    print(f"多样性摘要: {summary}")
```

## 🔍 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: No module named 'custom_scene_config'
   ```
   **解决方案**: 确保在正确的工作目录中运行脚本

2. **环境创建失败**
   ```
   KeyError: 'custom_diverse_pick_scene' not found
   ```
   **解决方案**: 检查环境是否正确注册到SimplerEnv

3. **CUDA内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   **解决方案**: 设置`CUDA_VISIBLE_DEVICES`或减少并发环境数量

### 调试技巧

1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **检查环境状态**
   ```python
   # 检查可用环境
   print(simpler_env.ENVIRONMENTS)
   
   # 检查环境映射
   print(simpler_env.ENVIRONMENT_MAP)
   ```

3. **验证配置**
   ```python
   from custom_scene_config import validate_config
   
   errors = validate_config(config)
   if errors:
       print("配置错误:", errors)
   ```

## 📊 性能优化

### 建议设置

- **训练阶段**: 使用`custom_enhanced_medium_diversity`
- **评估阶段**: 使用`custom_enhanced_high_diversity`
- **数据生成**: 使用`custom_training_data_generation`

### 资源管理

```python
# 批量处理时的资源管理
import gc

for i in range(num_episodes):
    env = simpler_env.make(env_name)
    # ... 运行环境
    env.close()
    del env
    
    if i % 10 == 0:  # 每10个episode清理一次
        gc.collect()
```

## 📝 配置文件示例

### 物体数据库配置

参考文件：
- `info_custom_diverse_objects_v1.json`: 基础多样化物体
- `info_custom_enhanced_objects_v1.json`: 增强版物体（包含材质属性）

### 场景配置示例

```json
{
  "scene_name": "training_scene_001",
  "target_objects": [
    {
      "object_type": "cube",
      "model_id": "green_cube_3cm",
      "scale": 1.0,
      "density": 1000
    }
  ],
  "distractor_objects": [
    {
      "object_type": "can", 
      "model_id": "coke_can",
      "scale": 1.2
    }
  ],
  "lighting": {
    "mode": "mixed",
    "enable_shadows": true
  }
}
```

---

## 📞 支持

如有问题或建议，请检查：
1. 测试脚本输出
2. 日志文件
3. 配置验证结果

更多详细信息请参考源代码注释和示例。
