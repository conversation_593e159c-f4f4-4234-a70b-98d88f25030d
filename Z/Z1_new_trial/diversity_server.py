#!/usr/bin/env python3
"""
SimplerEnv 多样性测试服务器

这个脚本提供了一个Web服务器，用于处理HTML界面的多样性测试请求。
"""

import os
import sys
import json
import asyncio
import subprocess
from pathlib import Path
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import threading
import time

# 设置环境变量
os.environ['MUJOCO_GL'] = 'egl'
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 添加路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / "SimplerEnv" / "ManiSkill2_real2sim"))
sys.path.insert(0, str(current_dir / "SimplerEnv"))

app = Flask(__name__)
CORS(app)

# 全局变量存储任务状态
task_status = {}

def run_diversity_test(material_intensity, lighting_intensity, task_id):
    """在后台运行多样性测试"""
    try:
        # 更新任务状态
        task_status[task_id] = {
            'status': 'running',
            'message': '正在生成多样性场景...',
            'start_time': datetime.now().isoformat()
        }
        
        # 导入必要模块
        import gymnasium as gym
        import simpler_env
        import sapien.core as sapien
        from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes import enhanced_diverse_env
        from diversity_enhancer import DiversityEnhancer
        import cv2
        import numpy as np
        
        # 关闭降噪
        sapien.render_config.rt_use_denoiser = False
        
        # 创建多样性增强器
        enhancer = DiversityEnhancer(
            seed=42,
            material_intensity=material_intensity,
            lighting_intensity=lighting_intensity
        )
        
        # 环境配置
        env_config = {
            "robot": "google_robot_static",
            "scene_name": "dummy_tabletop",
            "obs_mode": "image",
            "camera_cfgs": {"add_segmentation": True},
            "render_mode": "cameras",
            "num_objects_range": (4, 8),
            "diversity_level": "medium",
            "lighting_variation_prob": 1.0,
            "material_variation_prob": 1.0,
            "physics_variation_prob": 0.8
        }
        
        # 创建环境
        env = enhanced_diverse_env.EnhancedDiverseSceneEnv(**env_config)
        env.diversity_enhancer = enhancer
        
        # 重置环境以应用变化
        obs, info = env.reset()
        
        # 获取图像
        def get_image_from_obs(obs):
            if 'image' in obs and 'overhead_camera' in obs['image']:
                camera_obs = obs['image']['overhead_camera']
                if 'Color' in camera_obs:
                    return camera_obs['Color']
            return np.zeros((480, 640, 3), dtype=np.uint8)
        
        image = get_image_from_obs(obs)
        
        # 保存图像
        output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        image_filename = f"web_test_{material_intensity}_{lighting_intensity}_{timestamp}.png"
        image_path = output_dir / image_filename
        
        # 转换图像格式并保存
        if image.dtype == np.float32 or image.dtype == np.float64:
            image = (image * 255).astype(np.uint8)
        
        # 转换RGB到BGR用于OpenCV
        image_bgr = cv2.cvtColor(image[:,:,:3], cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(image_path), image_bgr)
        
        # 收集详细信息
        diversity_info = getattr(env, 'current_diversity_info', {})
        
        # 统计信息
        total_variations = 0
        objects_count = 0
        if 'materials' in diversity_info:
            objects_count = len(diversity_info['materials'])
            for mat_info in diversity_info['materials']:
                total_variations += len(mat_info.get('variations', []))
        
        # 获取物体列表
        try:
            actors = env.get_actors()
            actor_names = [actor.name for actor in actors]
        except:
            actor_names = []
        
        # 详细的多样性信息
        detailed_info = {
            'objects_count': len(actor_names),
            'object_names': actor_names,
            'lighting_changes': 'lighting' in diversity_info,
            'material_changes': 'materials' in diversity_info,
            'physics_changes': 'physics' in diversity_info,
            'total_variations': total_variations,
            'lighting_info': diversity_info.get('lighting', {}),
            'materials_info': diversity_info.get('materials', []),
            'physics_info': diversity_info.get('physics', {})
        }
        
        env.close()
        
        # 更新任务状态为成功
        task_status[task_id] = {
            'status': 'success',
            'message': '生成完成',
            'image_path': str(image_path),
            'details': detailed_info,
            'end_time': datetime.now().isoformat()
        }
        
    except Exception as e:
        # 更新任务状态为失败
        task_status[task_id] = {
            'status': 'error',
            'message': f'生成失败: {str(e)}',
            'end_time': datetime.now().isoformat()
        }

def run_diversity_test_with_config(config, task_id):
    """使用完整配置运行多样性测试"""
    try:
        print(f"开始运行多样性测试 - 配置: {config}")

        # 更新任务状态
        task_status[task_id] = {
            'status': 'running',
            'message': '正在生成多样性场景...',
            'start_time': datetime.now().isoformat()
        }

        # 导入必要模块
        import gymnasium as gym
        import simpler_env
        import sapien.core as sapien
        from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes import enhanced_diverse_env
        from diversity_enhancer import DiversityEnhancer
        import cv2
        import numpy as np

        # 关闭降噪
        sapien.render_config.rt_use_denoiser = False

        # 从配置中构建环境配置
        # 处理物体数量范围
        num_objects_range = config.get("num_objects_range", [4, 8])
        if isinstance(num_objects_range, list) and len(num_objects_range) >= 2:
            num_objects_tuple = (num_objects_range[0], num_objects_range[1])
        else:
            num_objects_tuple = (config.get("min_objects", 4), config.get("max_objects", 8))

        env_config = {
            "robot": config.get("robot_type", "google_robot_static"),
            "scene_name": config.get("scene_name", "dummy_tabletop"),
            "obs_mode": "image",
            "camera_cfgs": {"add_segmentation": True},
            "render_mode": "cameras",
            "num_objects_range": num_objects_tuple,
            "diversity_level": config.get("diversity_level", "medium"),
            "lighting_variation_prob": config.get("lighting_variation_prob", 1.0),
            "material_variation_prob": config.get("material_variation_prob", 1.0),
            "physics_variation_prob": config.get("physics_variation_prob", 0.8),
            "diversity_seed": config.get("random_seed", 42),
            # 直接传递强度参数给环境
            "material_intensity": config.get("material_intensity", "medium"),
            "lighting_intensity": config.get("lighting_intensity", "medium")
        }

        # 创建环境（现在会使用正确的强度参数）
        env = enhanced_diverse_env.EnhancedDiverseSceneEnv(**env_config)

        # 重置环境以应用变化
        obs, info = env.reset()

        # 获取图像
        def get_image_from_obs(obs):
            if 'image' in obs and 'overhead_camera' in obs['image']:
                camera_obs = obs['image']['overhead_camera']
                if 'Color' in camera_obs:
                    return camera_obs['Color']
            return np.zeros((480, 640, 3), dtype=np.uint8)

        image = get_image_from_obs(obs)

        # 保存图像
        output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
        output_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # 使用配置参数生成文件名
        material_int = config.get("material_intensity", "medium")
        lighting_int = config.get("lighting_intensity", "medium")
        robot_type = config.get("robot_type", "google_robot_static")
        scene_env = config.get("scene_name", "dummy_tabletop")  # 修正：使用scene_name

        image_filename = f"web_config_{material_int}_{lighting_int}_{robot_type}_{scene_env}_{timestamp}.png"
        image_path = output_dir / image_filename

        # 转换图像格式并保存
        if image.dtype == np.float32 or image.dtype == np.float64:
            image = (image * 255).astype(np.uint8)

        # 转换RGB到BGR用于OpenCV
        image_bgr = cv2.cvtColor(image[:,:,:3], cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(image_path), image_bgr)

        # 收集详细信息
        diversity_info = getattr(env, 'current_diversity_info', {})

        # 统计信息
        total_variations = 0
        objects_count = 0
        if 'materials' in diversity_info:
            objects_count = len(diversity_info['materials'])
            for mat_info in diversity_info['materials']:
                total_variations += len(mat_info.get('variations', []))

        # 收集详细信息用于返回
        details = {
            'objects_count': objects_count,
            'total_variations': total_variations,
            'lighting_changes': 'lighting' in diversity_info,
            'material_changes': 'materials' in diversity_info,
            'physics_changes': 'physics' in diversity_info,
            'config_used': config
        }

        # 更新任务状态为成功
        task_status[task_id] = {
            'status': 'success',  # 修正：使用'success'而不是'completed'
            'message': '多样性场景生成完成',
            'image_path': str(image_path),
            'image_filename': image_filename,
            'details': details,  # 添加详细信息
            'config_used': config,
            'diversity_info': diversity_info,
            'statistics': {
                'objects_count': objects_count,
                'total_variations': total_variations,
                'material_intensity': material_int,
                'lighting_intensity': lighting_int
            },
            'end_time': datetime.now().isoformat()
        }

    except Exception as e:
        # 更新任务状态为失败
        task_status[task_id] = {
            'status': 'error',
            'message': f'生成失败: {str(e)}',
            'config_used': config,
            'end_time': datetime.now().isoformat()
        }

@app.route('/')
def index():
    """提供HTML界面"""
    return send_from_directory('.', 'simple_diversity_interface.html')

@app.route('/advanced')
def advanced():
    """提供高级HTML界面"""
    return send_from_directory('.', 'final_hyperparams_interface.html')

@app.route('/complete')
def complete():
    """提供完整超参数界面"""
    return send_from_directory('.', 'final_hyperparams_interface.html')

@app.route('/test')
def test():
    """提供测试界面"""
    return send_from_directory('.', 'test_complete_interface.html')

@app.route('/legacy')
def legacy():
    """提供传统高级界面"""
    return send_from_directory('.', 'diversity_interactive.html')

@app.route('/api/generate', methods=['POST'])
def generate():
    """处理生成请求"""
    try:
        data = request.get_json()
        material_intensity = data.get('material_intensity', 'medium')
        lighting_intensity = data.get('lighting_intensity', 'medium')
        
        # 验证参数
        valid_intensities = ['subtle', 'medium', 'dramatic']
        if material_intensity not in valid_intensities or lighting_intensity not in valid_intensities:
            return jsonify({'error': '无效的强度参数'}), 400
        
        # 生成任务ID
        task_id = f"{material_intensity}_{lighting_intensity}_{int(time.time())}"
        
        # 在后台线程中运行测试
        thread = threading.Thread(
            target=run_diversity_test,
            args=(material_intensity, lighting_intensity, task_id)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'task_id': task_id,
            'status': 'started',
            'message': '任务已启动'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<task_id>')
def get_status(task_id):
    """获取任务状态"""
    if task_id not in task_status:
        return jsonify({'error': '任务不存在'}), 404
    
    return jsonify(task_status[task_id])

@app.route('/api/quick_test', methods=['POST'])
def quick_test():
    """快速测试接口 - 直接返回结果"""
    try:
        data = request.get_json()
        material_intensity = data.get('material_intensity', 'medium')
        lighting_intensity = data.get('lighting_intensity', 'medium')
        
        # 验证参数
        valid_intensities = ['subtle', 'medium', 'dramatic']
        if material_intensity not in valid_intensities or lighting_intensity not in valid_intensities:
            return jsonify({'error': '无效的强度参数'}), 400
        
        # 生成任务ID并立即执行
        task_id = f"quick_{material_intensity}_{lighting_intensity}_{int(time.time())}"
        
        # 直接运行测试（同步）
        run_diversity_test(material_intensity, lighting_intensity, task_id)
        
        # 返回结果
        if task_id in task_status:
            result = task_status[task_id]
            if result['status'] == 'success':
                return jsonify({
                    'success': True,
                    'image_path': result['image_path'],
                    'details': result['details']
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result['message']
                }), 500
        else:
            return jsonify({'error': '任务执行失败'}), 500
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/batch_generate', methods=['POST'])
def batch_generate():
    """批量生成所有组合"""
    try:
        intensities = ['subtle', 'medium', 'dramatic']
        task_ids = []
        
        for material in intensities:
            for lighting in intensities:
                task_id = f"batch_{material}_{lighting}_{int(time.time())}"
                task_ids.append(task_id)
                
                # 延迟启动每个任务
                def delayed_start(m, l, tid, delay):
                    time.sleep(delay)
                    run_diversity_test(m, l, tid)
                
                thread = threading.Thread(
                    target=delayed_start,
                    args=(material, lighting, task_id, len(task_ids) * 2)
                )
                thread.daemon = True
                thread.start()
        
        return jsonify({
            'task_ids': task_ids,
            'status': 'started',
            'message': f'已启动 {len(task_ids)} 个批量任务'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/advanced_generate', methods=['POST'])
def advanced_generate():
    """处理高级配置生成请求"""
    try:
        config = request.get_json()

        # 验证必要参数
        required_params = ['scene_name', 'robot_type', 'lighting_intensity', 'material_intensity']
        for param in required_params:
            if param not in config:
                return jsonify({'error': f'缺少必要参数: {param}'}), 400

        # 生成任务ID
        task_id = f"advanced_{config['material_intensity']}_{config['lighting_intensity']}_{int(time.time())}"

        # 在后台线程中运行高级测试
        thread = threading.Thread(
            target=run_advanced_diversity_test,
            args=(config, task_id)
        )
        thread.daemon = True
        thread.start()

        # 等待一段时间让任务完成（同步返回）
        time.sleep(3)

        # 检查任务状态并返回结果
        if task_id in task_status:
            result = task_status[task_id]
            if result['status'] == 'success':
                return jsonify({
                    'success': True,
                    'image_path': result['image_path'],
                    'details': result['details']
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result['message']
                }), 500
        else:
            return jsonify({'error': '任务执行失败'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

def run_advanced_diversity_test(config, task_id):
    """运行高级多样性测试"""
    try:
        # 更新任务状态
        task_status[task_id] = {
            'status': 'running',
            'message': '正在生成高级配置场景...',
            'start_time': datetime.now().isoformat()
        }

        # 导入必要模块
        import gymnasium as gym
        import sapien.core as sapien
        from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes import enhanced_diverse_env
        from diversity_enhancer import DiversityEnhancer
        import cv2
        import numpy as np

        # 关闭降噪
        sapien.render_config.rt_use_denoiser = False

        # 创建高级多样性增强器
        enhancer = DiversityEnhancer(
            seed=config.get('random_seed', 42),
            material_intensity=config['material_intensity'],
            lighting_intensity=config['lighting_intensity']
        )

        # 构建环境配置
        env_config = {
            "robot": config.get('robot_type', 'google_robot_static'),
            "scene_name": config.get('scene_name', 'dummy_tabletop'),
            "obs_mode": "image",
            "camera_cfgs": {"add_segmentation": True},
            "render_mode": "cameras",
            "num_objects_range": config.get('num_objects_range', [4, 8]),
            "diversity_level": config.get('diversity_level', 'medium'),
            "lighting_variation_prob": config.get('lighting_variation_prob', 1.0),
            "material_variation_prob": config.get('material_variation_prob', 1.0),
            "physics_variation_prob": config.get('physics_variation_prob', 0.8)
        }

        # 创建环境
        env = enhanced_diverse_env.EnhancedDiverseSceneEnv(**env_config)
        env.diversity_enhancer = enhancer

        # 重置环境以应用变化
        obs, info = env.reset()

        # 获取图像
        def get_image_from_obs(obs):
            if 'image' in obs and 'overhead_camera' in obs['image']:
                camera_obs = obs['image']['overhead_camera']
                if 'Color' in camera_obs:
                    return camera_obs['Color']
            return np.zeros((480, 640, 3), dtype=np.uint8)

        image = get_image_from_obs(obs)

        # 保存图像
        output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
        output_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        image_filename = f"advanced_{config['material_intensity']}_{config['lighting_intensity']}_{timestamp}.png"
        image_path = output_dir / image_filename

        # 转换图像格式并保存
        if image.dtype == np.float32 or image.dtype == np.float64:
            image = (image * 255).astype(np.uint8)

        # 转换RGB到BGR用于OpenCV
        image_bgr = cv2.cvtColor(image[:,:,:3], cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(image_path), image_bgr)

        # 收集详细信息
        diversity_info = getattr(env, 'current_diversity_info', {})

        # 统计信息
        total_variations = 0
        objects_count = 0
        if 'materials' in diversity_info:
            objects_count = len(diversity_info['materials'])
            for mat_info in diversity_info['materials']:
                total_variations += len(mat_info.get('variations', []))

        # 获取物体列表
        try:
            actors = env.get_actors()
            actor_names = [actor.name for actor in actors]
        except:
            actor_names = []

        # 详细的多样性信息
        detailed_info = {
            'objects_count': len(actor_names),
            'object_names': actor_names,
            'lighting_changes': 'lighting' in diversity_info,
            'material_changes': 'materials' in diversity_info,
            'physics_changes': 'physics' in diversity_info,
            'total_variations': total_variations,
            'config_used': config
        }

        env.close()

        # 更新任务状态为成功
        task_status[task_id] = {
            'status': 'success',
            'message': '高级配置生成完成',
            'image_path': str(image_path),
            'details': detailed_info,
            'end_time': datetime.now().isoformat()
        }

    except Exception as e:
        # 更新任务状态为失败
        task_status[task_id] = {
            'status': 'error',
            'message': f'高级配置生成失败: {str(e)}',
            'end_time': datetime.now().isoformat()
        }

@app.route('/api/complete_generate', methods=['POST'])
def complete_generate():
    """处理完整配置生成请求 - 使用简化的快速测试方法"""
    try:
        config = request.get_json()

        # 验证必要参数
        required_params = ['lighting_intensity', 'material_intensity']
        for param in required_params:
            if param not in config:
                return jsonify({'error': f'缺少必要参数: {param}'}), 400

        # 提取参数
        material_intensity = config['material_intensity']
        lighting_intensity = config['lighting_intensity']

        # 生成任务ID
        task_id = f"complete_{material_intensity}_{lighting_intensity}_{int(time.time())}"

        # 使用完整配置运行测试（同步）
        run_diversity_test_with_config(config, task_id)

        # 返回结果
        if task_id in task_status:
            result = task_status[task_id]
            if result['status'] == 'success':
                return jsonify({
                    'success': True,
                    'image_path': result['image_path'],
                    'details': result['details']
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result['message']
                }), 500
        else:
            return jsonify({'error': '任务执行失败'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500



if __name__ == '__main__':
    print("🚀 启动SimplerEnv多样性测试服务器...")
    print("📱 访问地址: http://localhost:5000")
    print("🔧 API端点:")
    print("   POST /api/generate - 异步生成")
    print("   POST /api/quick_test - 同步快速测试")
    print("   POST /api/batch_generate - 批量生成")
    print("   GET  /api/status/<task_id> - 获取任务状态")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=5000, debug=True, threaded=True)
