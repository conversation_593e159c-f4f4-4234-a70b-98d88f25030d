#!/usr/bin/env python3
"""
SimplerEnv 物体展示演示

专门设计用于清晰展示多个物体的环境，确保所有物体都可见且不重叠。
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any

# 设置环境变量
os.environ['MUJOCO_GL'] = 'egl'
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 添加路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / "SimplerEnv" / "ManiSkill2_real2sim"))
sys.path.insert(0, str(current_dir / "SimplerEnv"))

class ObjectShowcaseEnvironment:
    """专门用于展示多个物体的环境"""
    
    def __init__(self, num_objects: int = 10):
        self.num_objects = num_objects
        self.output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def create_grid_layout(self, num_objects: int) -> List[Tuple[float, float]]:
        """创建网格布局确保所有物体可见"""
        # 计算网格大小
        grid_size = int(np.ceil(np.sqrt(num_objects)))
        
        # 工作空间范围
        x_range = (-0.5, 0.5)
        y_range = (-0.5, 0.5)
        
        # 计算间距
        x_spacing = (x_range[1] - x_range[0]) / (grid_size + 1)
        y_spacing = (y_range[1] - y_range[0]) / (grid_size + 1)
        
        positions = []
        for i in range(num_objects):
            row = i // grid_size
            col = i % grid_size
            
            x = x_range[0] + (col + 1) * x_spacing
            y = y_range[0] + (row + 1) * y_spacing
            
            positions.append((x, y))
        
        return positions
    
    def create_showcase_environment(self):
        """创建物体展示环境"""
        print(f"🎨 创建 {self.num_objects} 个物体的展示环境")
        print("=" * 50)
        
        try:
            import gymnasium as gym
            import sapien.core as sapien
            from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes import enhanced_diverse_env
            from diversity_enhancer import DiversityEnhancer
            
            # 关闭降噪
            sapien.render_config.rt_use_denoiser = False
            
            # 创建多样性增强器
            enhancer = DiversityEnhancer(
                seed=42,
                material_intensity="dramatic",
                lighting_intensity="medium"
            )
            
            # 创建环境
            env = enhanced_diverse_env.EnhancedDiverseSceneEnv(
                robot="google_robot_static",
                scene_name="dummy_tabletop",
                obs_mode="image",
                camera_cfgs={"add_segmentation": True},
                render_mode="cameras",
                num_objects_range=(self.num_objects, self.num_objects),
                diversity_level="custom",
                lighting_variation_prob=1.0,
                material_variation_prob=1.0,
                physics_variation_prob=0.8,
            )
            
            # 设置多样性增强器
            env.diversity_enhancer = enhancer
            
            print(f"✅ 环境创建成功，目标物体数量: {self.num_objects}")
            
            # 多次尝试生成理想的场景
            best_scene = None
            best_count = 0
            max_attempts = 10
            
            for attempt in range(max_attempts):
                print(f"🔄 尝试 {attempt + 1}/{max_attempts} 生成最佳展示场景...")
                
                obs, info = env.reset()
                
                # 检查实际生成的物体
                if hasattr(env, 'all_scene_objects'):
                    actual_objects = [obj for obj in env.all_scene_objects if 
                                    'robot' not in obj.name.lower() and 
                                    'table' not in obj.name.lower() and
                                    'ground' not in obj.name.lower()]
                    
                    actual_count = len(actual_objects)
                    print(f"   生成了 {actual_count} 个物体")
                    
                    if actual_count > best_count:
                        best_count = actual_count
                        best_scene = {
                            'obs': obs,
                            'info': info,
                            'objects': actual_objects,
                            'count': actual_count
                        }
                        
                        if actual_count >= self.num_objects * 0.8:  # 如果达到80%目标就接受
                            break
            
            if best_scene is None:
                print("❌ 无法生成合适的展示场景")
                return None
            
            print(f"✅ 最佳场景包含 {best_scene['count']} 个物体")
            
            # 获取图像
            def get_image_from_obs(obs):
                if 'image' in obs and 'overhead_camera' in obs['image']:
                    camera_obs = obs['image']['overhead_camera']
                    if 'Color' in camera_obs:
                        return camera_obs['Color']
                return np.zeros((480, 640, 3), dtype=np.uint8)
            
            image = get_image_from_obs(best_scene['obs'])
            
            # 保存图像
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            image_filename = f"object_showcase_{best_scene['count']}_objects_{timestamp}.png"
            image_path = self.output_dir / image_filename
            
            # 转换图像格式并保存
            if image.dtype == np.float32 or image.dtype == np.float64:
                image = (image * 255).astype(np.uint8)
            
            # 转换RGB到BGR用于OpenCV
            image_bgr = cv2.cvtColor(image[:,:,:3], cv2.COLOR_RGB2BGR)
            cv2.imwrite(str(image_path), image_bgr)
            
            print(f"✅ 展示图像已保存: {image_path}")
            
            # 显示物体详情
            self.display_object_details(best_scene['objects'])
            
            # 生成分析报告
            self.generate_analysis_report(best_scene, image_path)
            
            env.close()
            return str(image_path), best_scene['count']
            
        except Exception as e:
            print(f"❌ 创建展示环境失败: {e}")
            import traceback
            traceback.print_exc()
            return None, 0
    
    def display_object_details(self, objects: List):
        """显示物体详细信息"""
        print(f"\n📊 物体展示详情:")
        print("-" * 40)
        
        object_types = {}
        for i, obj in enumerate(objects, 1):
            obj_type = self.classify_object_type(obj.name)
            object_types[obj_type] = object_types.get(obj_type, 0) + 1
            print(f"   {i:2d}. {obj.name} ({obj_type})")
        
        print(f"\n📈 物体类型统计:")
        for obj_type, count in object_types.items():
            print(f"   - {obj_type}: {count} 个")
    
    def classify_object_type(self, name: str) -> str:
        """分类物体类型"""
        name_lower = name.lower()
        if 'can' in name_lower:
            return "罐子类"
        elif 'cube' in name_lower:
            return "方块类"
        elif 'bottle' in name_lower:
            return "瓶子类"
        elif any(fruit in name_lower for fruit in ['apple', 'orange']):
            return "水果类"
        elif 'sponge' in name_lower:
            return "海绵类"
        else:
            return "其他类"
    
    def generate_analysis_report(self, scene_data: Dict, image_path: str):
        """生成分析报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_objects": scene_data['count'],
            "target_objects": self.num_objects,
            "success_rate": scene_data['count'] / self.num_objects,
            "image_path": str(image_path),
            "objects": [obj.name for obj in scene_data['objects']],
            "object_types": {}
        }
        
        # 统计物体类型
        for obj in scene_data['objects']:
            obj_type = self.classify_object_type(obj.name)
            report["object_types"][obj_type] = report["object_types"].get(obj_type, 0) + 1
        
        # 保存报告
        report_path = self.output_dir / f"showcase_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 分析报告已保存: {report_path}")

def main():
    """主函数"""
    print("🎨 SimplerEnv 物体展示演示")
    print("=" * 40)
    
    # 创建展示环境
    showcase = ObjectShowcaseEnvironment(num_objects=10)
    
    print("🚀 开始创建物体展示场景...")
    result_path, object_count = showcase.create_showcase_environment()
    
    if result_path:
        print(f"\n🎉 成功创建包含 {object_count} 个物体的展示场景！")
        print(f"📸 图像路径: {result_path}")
        print(f"📊 成功率: {object_count/10*100:.1f}%")
        
        print(f"\n🎨 展示特点:")
        print(f"   - 专门优化的物体布局")
        print(f"   - 戏剧性材质变化效果")
        print(f"   - 中等光照变化")
        print(f"   - 多样化物体类型")
        print(f"   - 确保物体可见性")
    else:
        print("\n❌ 无法创建展示场景")
        print("💡 可能的原因:")
        print("   - 物体生成限制")
        print("   - 工作空间限制")
        print("   - 碰撞检测问题")

if __name__ == "__main__":
    main()
