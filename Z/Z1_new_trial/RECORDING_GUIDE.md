# 🎬 自定义场景画面录制指南

本指南详细说明如何录制和保存SimplerEnv自定义多样化场景的画面。

## 📋 目录

- [快速开始](#快速开始)
- [录制脚本说明](#录制脚本说明)
- [运行方式](#运行方式)
- [输出文件说明](#输出文件说明)
- [故障排除](#故障排除)

## 🚀 快速开始

### 方法1: 快速演示录制（推荐）

```bash
# 进入实验目录
cd /home/<USER>/claude/SpatialVLA/Z/Z1_new_trial

# 运行快速录制脚本
python quick_record.py
```

**特点**:
- ⚡ 快速执行（约2-3分钟）
- 🎯 录制2个代表性环境
- 📹 生成MP4视频和PNG图像
- 💾 输出到 `quick_demo/` 目录

### 方法2: 完整录制（详细版）

```bash
# 进入实验目录
cd /home/<USER>/claude/SpatialVLA/Z/Z1_new_trial

# 运行完整录制脚本
python record_custom_scenes.py
```

**特点**:
- 🔄 录制所有4个自定义环境
- 📊 每个环境3个episodes
- 📝 生成详细的JSON报告
- 📁 完整的文件组织结构

## 📝 录制脚本说明

### 1. `quick_record.py` - 快速演示脚本

**功能**:
- 录制2个代表性环境的短视频
- 每个环境运行20步
- 保存视频和第一帧图像

**适用场景**:
- 快速查看场景效果
- 演示用途
- 初次测试

### 2. `record_custom_scenes.py` - 完整录制脚本

**功能**:
- 录制所有自定义环境
- 每个环境多个episodes
- 详细的配置信息记录
- 完整的报告生成

**适用场景**:
- 完整的场景文档
- 详细分析
- 正式记录

## 🎯 运行方式

### 环境要求

确保您在正确的环境中：
```bash
# 检查当前位置
pwd
# 应该显示: /home/<USER>/claude/SpatialVLA/Z/Z1_new_trial 或类似路径

# 检查Python环境
which python
# 确保使用正确的conda环境
```

### 运行步骤

#### 步骤1: 进入实验目录
```bash
cd /home/<USER>/claude/SpatialVLA/Z/Z1_new_trial
```

#### 步骤2: 选择录制方式

**选项A - 快速演示**:
```bash
python quick_record.py
```

**选项B - 完整录制**:
```bash
python record_custom_scenes.py
```

#### 步骤3: 等待完成

录制过程中会显示进度信息：
```
🎬 SimplerEnv 自定义场景录制工具
============================================================
设置录制环境...
✓ 录制环境设置完成

🎬 开始录制环境: custom_diverse_pick_scene
============================================================
✓ 环境 custom_diverse_pick_scene 创建成功

--- 录制 Episode 1/3 ---
任务指令: Pick up the green cube
场景配置: 总物体数 5
多样性变化: ['lighting', 'materials']
✓ Episode 1 录制完成: 31 帧
...
```

## 📁 输出文件说明

### 快速录制输出 (`quick_demo/`)

```
quick_demo/
├── custom_diverse_pick_scene_143052.mp4           # 基础场景视频
├── custom_diverse_pick_scene_first_frame.png      # 基础场景第一帧
├── custom_enhanced_medium_diversity_143055.mp4    # 中等多样性场景视频
└── custom_enhanced_medium_diversity_first_frame.png # 中等多样性场景第一帧
```

### 完整录制输出 (`custom_scenes_recordings_YYYYMMDD_HHMMSS/`)

```
custom_scenes_recordings_20250707_143000/
├── README.md                           # 录制说明文档
├── recording_summary.json              # 总体录制报告
├── custom_diverse_pick_scene/          # 基础多样化场景
│   ├── episode_01_seed_42_143001.mp4  # Episode 1 视频
│   ├── episode_02_seed_1042_143002.mp4 # Episode 2 视频
│   ├── episode_03_seed_2042_143003.mp4 # Episode 3 视频
│   ├── episode_01_first_frame.png     # Episode 1 第一帧
│   ├── episode_01_last_frame.png      # Episode 1 最后一帧
│   ├── episode_02_first_frame.png     # Episode 2 第一帧
│   ├── episode_02_last_frame.png      # Episode 2 最后一帧
│   ├── episode_03_first_frame.png     # Episode 3 第一帧
│   ├── episode_03_last_frame.png      # Episode 3 最后一帧
│   └── recording_info.json            # 详细录制信息
├── custom_enhanced_low_diversity/      # 低多样性场景
├── custom_enhanced_medium_diversity/   # 中等多样性场景
└── custom_enhanced_high_diversity/     # 高多样性场景
```

### 文件类型说明

- **`.mp4` 视频文件**: 包含完整的episode录制
- **`.png` 图像文件**: 关键帧的静态图像
- **`.json` 信息文件**: 包含详细的配置和元数据
- **`README.md`**: 人类可读的录制说明

## 🎥 查看录制结果

### 查看视频
```bash
# 使用系统默认播放器
xdg-open quick_demo/custom_diverse_pick_scene_*.mp4

# 或使用VLC播放器
vlc quick_demo/custom_diverse_pick_scene_*.mp4

# 或使用ffplay
ffplay quick_demo/custom_diverse_pick_scene_*.mp4
```

### 查看图像
```bash
# 使用图像查看器
eog quick_demo/custom_diverse_pick_scene_first_frame.png

# 或使用其他查看器
display quick_demo/custom_diverse_pick_scene_first_frame.png
```

### 查看录制信息
```bash
# 查看JSON报告
cat custom_scenes_recordings_*/recording_summary.json | jq .

# 查看README
cat custom_scenes_recordings_*/README.md
```

## 🔧 自定义录制参数

### 修改录制参数

编辑 `record_custom_scenes.py` 中的参数：

```python
# 在 record_all_custom_environments() 函数中修改
results = record_single_environment(
    env_name=env_name,
    num_episodes=5,        # 改为5个episodes
    steps_per_episode=60,  # 改为60步
    output_dir=str(main_output_dir)
)
```

### 选择特定环境

修改要录制的环境列表：

```python
# 在 record_all_custom_environments() 函数中修改
environments_to_record = [
    "custom_enhanced_high_diversity",  # 只录制高多样性环境
    # "custom_diverse_pick_scene",     # 注释掉不需要的环境
]
```

### 调整视频质量

修改视频参数：

```python
# 在 save_video() 函数中修改
def save_video(images, output_path, fps=15):  # 提高帧率
    # ...
    fourcc = cv2.VideoWriter_fourcc(*'H264')  # 使用H264编码
```

## 🔍 故障排除

### 常见问题

#### 1. 显示错误
```
GLFW error: X11: The DISPLAY environment variable is missing
```
**解决方案**: 这是正常的，不影响录制。脚本会继续运行。

#### 2. 环境创建失败
```
KeyError: 'custom_diverse_pick_scene' not found
```
**解决方案**: 确保在正确的目录中运行脚本，并且环境变量设置正确。

#### 3. 图像提取失败
```
⚠ 无法找到图像数据，返回空图像
```
**解决方案**: 检查观察空间结构，可能需要调整图像提取逻辑。

#### 4. 视频保存失败
```
✗ 视频保存失败: ...
```
**解决方案**: 
- 检查磁盘空间
- 确保有写入权限
- 尝试不同的视频编码器

### 调试技巧

#### 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 检查环境状态
```bash
# 运行简单测试
python -c "
import sys
sys.path.append('SimplerEnv')
import simpler_env
print('可用环境:', [e for e in simpler_env.ENVIRONMENTS if 'custom' in e])
"
```

#### 手动测试单个环境
```python
import sys
sys.path.append('SimplerEnv')
import simpler_env

env = simpler_env.make('custom_diverse_pick_scene')
obs, info = env.reset(seed=42)
print('观察空间键:', list(obs.keys()))
if 'image' in obs:
    print('图像键:', list(obs['image'].keys()))
```

## 📊 性能优化

### 加速录制

1. **减少步数**: 将 `steps_per_episode` 设为较小值（如20）
2. **减少episodes**: 将 `num_episodes` 设为1-2
3. **选择特定环境**: 只录制需要的环境

### 提高质量

1. **增加帧率**: 将 `fps` 设为15-30
2. **更高分辨率**: 如果支持，使用更高分辨率的相机
3. **更长录制**: 增加 `steps_per_episode` 到100+

---

## 📞 获取帮助

如果遇到问题：

1. 检查错误信息和日志
2. 确认环境变量和路径设置
3. 尝试运行简化版本
4. 查看生成的JSON报告了解详细信息

录制愉快！🎬
