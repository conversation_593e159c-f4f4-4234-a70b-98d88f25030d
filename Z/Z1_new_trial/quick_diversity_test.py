#!/usr/bin/env python3
"""
SimplerEnv 快速多样性测试工具

这个脚本提供了一个快速测试不同多样性强度的工具，
可以快速生成和比较不同参数设置的效果。

使用方法:
python quick_diversity_test.py [材质强度] [光照强度]

例如:
python quick_diversity_test.py dramatic dramatic
python quick_diversity_test.py medium subtle
"""

import os
import sys
import numpy as np
from pathlib import Path
from datetime import datetime

# 设置环境变量
os.environ['MUJOCO_GL'] = 'egl'
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 添加路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / "SimplerEnv" / "ManiSkill2_real2sim"))
sys.path.insert(0, str(current_dir / "SimplerEnv"))

def print_detailed_diversity_info(env, obs):
    """打印详细的多样性信息，包括每个物体的具体变化"""
    if not hasattr(env, 'current_diversity_info'):
        print("⚠️ 未找到多样性信息")
        return
    
    diversity_info = env.current_diversity_info
    print("\n📊 详细多样性变化报告:")
    print("=" * 60)
    
    # 获取场景中的所有物体信息
    try:
        actors = env.get_actors()
        actor_names = [actor.name for actor in actors]
        print(f"🎯 场景物体总数: {len(actor_names)}")
        print(f"   物体列表: {', '.join(actor_names)}")
    except Exception as e:
        print(f"⚠️ 无法获取物体列表: {e}")
        actor_names = []
    
    print()
    
    # 详细光照信息
    if 'lighting' in diversity_info:
        lighting = diversity_info['lighting']
        print("🔆 光照系统变化:")
        print(f"   类型: {lighting.get('type', '未知')}")
        
        if 'lights' in lighting:
            lights = lighting['lights']
            print(f"   光源数量: {len(lights)}")
            for i, light in enumerate(lights):
                print(f"   光源 {i+1}:")
                print(f"     - 类型: {light.get('type', '未知')}")
                if 'direction' in light:
                    direction = light['direction']
                    print(f"     - 方向: ({direction[0]:.2f}, {direction[1]:.2f}, {direction[2]:.2f})")
                if 'color' in light:
                    color = light['color']
                    print(f"     - 颜色: RGB({color[0]:.2f}, {color[1]:.2f}, {color[2]:.2f})")
                if 'intensity' in light:
                    print(f"     - 强度: {light['intensity']:.2f}")
        
        if 'ambient' in lighting:
            ambient = lighting['ambient']
            print(f"   环境光: RGB({ambient[0]:.2f}, {ambient[1]:.2f}, {ambient[2]:.2f})")
    else:
        print("🔆 光照系统: 使用默认设置")
    
    print()
    
    # 详细材质信息
    if 'materials' in diversity_info:
        materials = diversity_info['materials']
        print(f"🎨 材质变化详情 ({len(materials)} 个物体):")
        
        for i, mat_info in enumerate(materials):
            obj_name = mat_info.get('object_name', f'物体_{i+1}')
            variations = mat_info.get('variations', [])
            
            print(f"\n   📦 物体: {obj_name}")
            if not variations:
                print("     - 无材质变化")
                continue
            
            for var in variations:
                var_type = var.get('type', '未知')
                if var_type == 'color':
                    color = var.get('color', [0, 0, 0])
                    print(f"     - 颜色变化: RGB({color[0]:.2f}, {color[1]:.2f}, {color[2]:.2f})")
                    if 'metallic' in var:
                        print(f"       金属度: {var['metallic']:.2f}")
                    if 'roughness' in var:
                        print(f"       粗糙度: {var['roughness']:.2f}")
                    if 'applied_shapes' in var:
                        print(f"       应用形状数: {var['applied_shapes']}")
                
                elif var_type == 'friction':
                    print(f"     - 摩擦力变化:")
                    if 'multiplier' in var:
                        print(f"       倍数: {var['multiplier']:.2f}")
                    if 'static_friction' in var:
                        print(f"       静摩擦: {var['static_friction']:.3f}")
                    if 'dynamic_friction' in var:
                        print(f"       动摩擦: {var['dynamic_friction']:.3f}")
                
                elif var_type == 'restitution':
                    print(f"     - 弹性变化: {var.get('value', 0):.3f}")
                
                elif var_type == 'density':
                    print(f"     - 密度变化: 倍数 {var.get('multiplier', 1):.2f}")
    else:
        print("🎨 材质系统: 使用默认设置")
    
    print()
    
    # 物理属性信息
    if 'physics' in diversity_info:
        physics = diversity_info['physics']
        print("⚙️ 物理属性变化:")
        if 'gravity' in physics:
            print(f"   重力: {physics['gravity']:.2f} m/s²")
        if 'timestep' in physics:
            print(f"   时间步长: {physics['timestep']:.6f} s")
        if 'contact_offset' in physics:
            print(f"   接触偏移: {physics['contact_offset']}")
    else:
        print("⚙️ 物理系统: 使用默认设置")
    
    print()
    
    # 统计摘要
    print("📈 变化统计摘要:")
    print("-" * 30)
    
    total_variations = 0
    if 'materials' in diversity_info:
        for mat_info in diversity_info['materials']:
            total_variations += len(mat_info.get('variations', []))
    
    print(f"   总变化数量: {total_variations}")
    print(f"   受影响物体: {len(diversity_info.get('materials', []))}")
    print(f"   光照变化: {'是' if 'lighting' in diversity_info else '否'}")
    print(f"   物理变化: {'是' if 'physics' in diversity_info else '否'}")
    
    print("-" * 60)


def quick_test(material_intensity="medium", lighting_intensity="medium"):
    """快速测试指定强度的多样性效果"""
    print(f"🚀 快速测试多样性效果")
    print(f"   材质强度: {material_intensity}")
    print(f"   光照强度: {lighting_intensity}")
    print("=" * 50)
    
    try:
        # 导入必要模块
        import gymnasium as gym
        import simpler_env
        import sapien.core as sapien
        from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes import enhanced_diverse_env
        from diversity_enhancer import DiversityEnhancer
        import cv2
        
        # 关闭降噪
        sapien.render_config.rt_use_denoiser = False
        
        print("✅ 模块导入成功")
        
        # 创建多样性增强器
        enhancer = DiversityEnhancer(
            seed=42,
            material_intensity=material_intensity,
            lighting_intensity=lighting_intensity
        )
        
        # 环境配置
        env_config = {
            "robot": "google_robot_static",
            "scene_name": "dummy_tabletop",
            "obs_mode": "image",
            "camera_cfgs": {"add_segmentation": True},
            "render_mode": "cameras",
            "num_objects_range": (4, 8),
            "diversity_level": "medium",
            "lighting_variation_prob": 1.0,
            "material_variation_prob": 1.0,
            "physics_variation_prob": 0.8
        }
        
        # 创建环境
        env = enhanced_diverse_env.EnhancedDiverseSceneEnv(**env_config)
        env.diversity_enhancer = enhancer
        
        print("✅ 环境创建成功")
        
        # 重置环境以应用变化
        obs, info = env.reset()
        print("✅ 环境重置成功，多样性变化已应用")
        
        # 获取图像
        def get_image_from_obs(obs):
            if 'image' in obs and 'overhead_camera' in obs['image']:
                camera_obs = obs['image']['overhead_camera']
                if 'Color' in camera_obs:
                    return camera_obs['Color']
            return np.zeros((480, 640, 3), dtype=np.uint8)
        
        image = get_image_from_obs(obs)
        
        # 保存图像
        output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        image_filename = f"quick_test_{material_intensity}_{lighting_intensity}_{timestamp}.png"
        image_path = output_dir / image_filename
        
        # 转换图像格式并保存
        if image.dtype == np.float32 or image.dtype == np.float64:
            image = (image * 255).astype(np.uint8)
        
        # 转换RGB到BGR用于OpenCV
        image_bgr = cv2.cvtColor(image[:,:,:3], cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(image_path), image_bgr)
        
        print(f"✅ 图像已保存: {image_path}")
        
        # 显示详细的多样性信息
        print_detailed_diversity_info(env, obs)
        
        env.close()
        
        print(f"\n🎉 快速测试完成！")
        print(f"📁 输出文件: {image_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 解析命令行参数
    material_intensity = "medium"
    lighting_intensity = "medium"
    
    if len(sys.argv) >= 2:
        material_intensity = sys.argv[1]
    if len(sys.argv) >= 3:
        lighting_intensity = sys.argv[2]
    
    # 验证参数
    valid_intensities = ["subtle", "medium", "dramatic"]
    if material_intensity not in valid_intensities:
        print(f"❌ 无效的材质强度: {material_intensity}")
        print(f"   有效选项: {', '.join(valid_intensities)}")
        sys.exit(1)
    
    if lighting_intensity not in valid_intensities:
        print(f"❌ 无效的光照强度: {lighting_intensity}")
        print(f"   有效选项: {', '.join(valid_intensities)}")
        sys.exit(1)
    
    # 运行测试
    success = quick_test(material_intensity, lighting_intensity)
    
    if success:
        print("\n✅ 测试成功完成！")
    else:
        print("\n❌ 测试失败！")
