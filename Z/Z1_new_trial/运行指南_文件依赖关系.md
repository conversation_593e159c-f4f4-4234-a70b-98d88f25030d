# SimplerEnv 材质光照变化 - 运行指南和文件依赖关系

## 🎯 推荐运行方式

### 最简单的方式（推荐）

```bash
cd /home/<USER>/claude/SpatialVLA/Z/Z1_new_trial
python diversity_hyperparams.py
```

这是**最简单**的方式，只需要：
1. 修改 `diversity_hyperparams.py` 中的参数
2. 直接运行即可

## 📊 三个主要脚本对比

| 脚本名称 | 用途 | 复杂度 | 推荐程度 |
|---------|------|--------|----------|
| `diversity_hyperparams.py` | 简单超参数接口 | ⭐ | ⭐⭐⭐⭐⭐ |
| `enhanced_diversity_demo.py` | 完整演示和对比 | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| `simpler_env_demo.py` | 基础演示 | ⭐⭐ | ⭐⭐ |

## 🔗 文件依赖关系图

```
diversity_hyperparams.py (主要运行文件)
├── SimplerEnv/
│   ├── diversity_enhancer.py (核心多样性增强器)
│   ├── ManiSkill2_real2sim/
│   │   └── mani_skill2_real2sim/envs/custom_scenes/
│   │       └── enhanced_diverse_env.py (增强环境)
│   └── custom_scene_config.py (场景配置)
├── 系统依赖:
│   ├── gymnasium
│   ├── simpler_env
│   ├── sapien.core
│   ├── cv2 (OpenCV)
│   └── numpy
└── 输出:
    ├── /home/<USER>/claude/SpatialVLA/Z/Z_new_trial/custom_diversity_*.png
    └── 控制台输出的多样性信息
```

## 📝 详细文件说明

### 1. `diversity_hyperparams.py` (主要运行文件)

**作用**: 提供最简单的超参数配置接口

**依赖的文件**:
- `SimplerEnv/diversity_enhancer.py`
- `SimplerEnv/ManiSkill2_real2sim/mani_skill2_real2sim/envs/custom_scenes/enhanced_diverse_env.py`

**运行方式**:
```bash
python diversity_hyperparams.py
```

**输出**:
- 单张图像: `custom_diversity_{材质强度}_{光照强度}_{时间戳}.png`
- 控制台显示详细的多样性变化信息

### 2. `enhanced_diversity_demo.py` (完整演示)

**作用**: 提供完整的演示功能，包括对比图像生成

**依赖的文件**:
- 与 `diversity_hyperparams.py` 相同
- 额外生成对比图像和JSON报告

**运行方式**:
```bash
# 单一配置
python enhanced_diversity_demo.py --material-intensity dramatic --lighting-intensity dramatic

# 所有组合
python enhanced_diversity_demo.py --demo-all

# 自定义参数
python enhanced_diversity_demo.py --custom-color-intensity 3.0
```

**输出**:
- 对比图像: `diversity_comparison_{材质}_{光照}_{时间戳}.png`
- JSON报告: `diversity_demo_report_{时间戳}.json`

### 3. `simpler_env_demo.py` (基础演示)

**作用**: 基础的SimplerEnv演示

**运行方式**:
```bash
python simpler_env_demo.py --mode demo
```

## 🔧 核心依赖文件详解

### `SimplerEnv/diversity_enhancer.py`
- **作用**: 核心的多样性增强器类
- **功能**: 
  - 材质变化（颜色、摩擦力、弹性等）
  - 光照变化（环境光、方向光、彩色光等）
  - 三种强度级别（subtle, medium, dramatic）

### `SimplerEnv/ManiSkill2_real2sim/.../enhanced_diverse_env.py`
- **作用**: 增强的多样性环境类
- **功能**:
  - 集成多样性增强器
  - 环境重置时自动应用变化
  - 记录和报告多样性信息

### `SimplerEnv/custom_scene_config.py`
- **作用**: 自定义场景配置
- **功能**:
  - 物体数据库管理
  - 随机场景生成
  - 光照配置

## 🚀 快速开始步骤

### 步骤1: 选择运行方式

**推荐新手**: 使用 `diversity_hyperparams.py`
```bash
cd /home/<USER>/claude/SpatialVLA/Z/Z1_new_trial
python diversity_hyperparams.py
```

**需要对比效果**: 使用 `enhanced_diversity_demo.py`
```bash
python enhanced_diversity_demo.py --demo-all
```

### 步骤2: 修改参数（可选）

编辑 `diversity_hyperparams.py` 中的参数：

```python
# 基础配置
MATERIAL_INTENSITY = "dramatic"  # subtle, medium, dramatic
LIGHTING_INTENSITY = "dramatic"  # subtle, medium, dramatic

# 材质参数
MATERIAL_HYPERPARAMS = {
    "friction_range": [0.05, 5.0],
    "color_variations": [
        (3.0, 0.2, 0.2),    # 超鲜红
        (0.2, 3.0, 0.2),    # 超鲜绿
        # ... 更多颜色
    ]
}

# 光照参数  
LIGHTING_HYPERPARAMS = {
    "directional_intensity_range": [0.1, 8.0],
    "colored_light_probability": 0.6
}
```

### 步骤3: 查看结果

**图像输出位置**:
```
/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/
├── custom_diversity_*.png (单张图像)
├── diversity_comparison/ (对比图像)
└── diversity_reports/ (详细报告)
```

**控制台输出示例**:
```
📊 应用的多样性变化:
🔆 光照变化: directional_only
   光源数量: 1
🎨 材质变化: 5 个物体
   - 颜色变化: 3 个物体
   - 摩擦力变化: 4 个物体
   - 弹性变化: 2 个物体
```

## ⚠️ 常见问题

### 1. 找不到模块错误
```bash
ModuleNotFoundError: No module named 'diversity_enhancer'
```
**解决**: 确保在正确目录运行：
```bash
cd /home/<USER>/claude/SpatialVLA/Z/Z1_new_trial
```

### 2. 路径错误
```bash
FileNotFoundError: SimplerEnv/diversity_enhancer.py
```
**解决**: 检查文件结构，确保 SimplerEnv 目录存在

### 3. 图像保存失败
**解决**: 检查输出目录权限和磁盘空间

## 💡 使用建议

1. **新手**: 直接运行 `diversity_hyperparams.py`，观察效果
2. **调参**: 修改 `diversity_hyperparams.py` 中的参数，重新运行
3. **对比**: 使用 `enhanced_diversity_demo.py --demo-all` 生成所有组合
4. **自定义**: 通过编程接口创建完全自定义的效果

## 🎯 总结

**最简单的使用方式**:
```bash
cd /home/<USER>/claude/SpatialVLA/Z/Z1_new_trial
python diversity_hyperparams.py
```

这个命令会：
- 使用当前配置的超参数
- 生成一张展示材质和光照变化的图像
- 在控制台显示详细的变化信息
- 保存图像到指定目录

**文件依赖关系简化版**:
```
diversity_hyperparams.py → SimplerEnv/diversity_enhancer.py → 材质光照变化效果
```
