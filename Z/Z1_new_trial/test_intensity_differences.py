#!/usr/bin/env python3
"""
测试不同强度设置的效果差异
"""

import requests
import json
import time
from pathlib import Path

def test_intensity_combination(material_intensity, lighting_intensity):
    """测试特定的强度组合"""
    print(f"\n🧪 测试组合: 材质={material_intensity}, 光照={lighting_intensity}")
    
    config = {
        "lighting_intensity": lighting_intensity,
        "material_intensity": material_intensity,
        "scene_name": "dummy_tabletop",
        "robot_type": "google_robot_static",
        "num_objects_range": [4, 6],
        "random_seed": 42  # 使用固定种子便于比较
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/api/complete_generate',
            headers={'Content-Type': 'application/json'},
            json=config,
            timeout=60
        )
        
        if response.ok:
            data = response.json()
            if data.get('success'):
                print(f"✅ 成功生成图片: {data['image_path']}")
                print(f"📊 详情: {data['details']}")
                return data['image_path']
            else:
                print(f"❌ 生成失败: {data.get('error', '未知错误')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    return None

def main():
    """主测试函数"""
    print("🎯 开始测试不同强度设置的效果差异")
    print("=" * 60)
    
    # 测试所有强度组合
    intensities = ['subtle', 'medium', 'dramatic']
    results = {}
    
    for material in intensities:
        for lighting in intensities:
            combination = f"{material}_{lighting}"
            image_path = test_intensity_combination(material, lighting)
            results[combination] = image_path
            time.sleep(2)  # 避免请求过快
    
    print("\n📋 测试结果汇总:")
    print("=" * 60)
    for combo, path in results.items():
        material, lighting = combo.split('_')
        status = "✅" if path else "❌"
        print(f"{status} {material:>8} 材质 + {lighting:>8} 光照: {path or '失败'}")
    
    # 检查生成的图片
    output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
    if output_dir.exists():
        print(f"\n📁 输出目录: {output_dir}")
        png_files = list(output_dir.glob("web_config_*.png"))
        print(f"📸 共生成 {len(png_files)} 张图片")
        
        # 按时间排序显示最新的几张
        png_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        print("\n🕒 最新生成的图片:")
        for i, file in enumerate(png_files[:9]):  # 显示最新的9张
            print(f"   {i+1}. {file.name}")

if __name__ == "__main__":
    main()
