#!/usr/bin/env python3
"""
修复渲染问题的SimplerEnv演示
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path

# 设置环境变量
os.environ['DISPLAY'] = ':0'  # 尝试设置显示
os.environ['MUJOCO_GL'] = 'egl'  # 使用EGL渲染

# 添加路径
sys.path.insert(0, '/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv')
sys.path.insert(0, '/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/ManiSkill2_real2sim')

def setup_environment():
    """设置环境"""
    try:
        import sapien.core as sapien
        
        # 配置渲染器
        sapien.render_config.rt_use_denoiser = False
        sapien.render_config.camera_shader_dir = "rt"
        
        from mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env import DiverseEnhancedSceneEnv
        
        print("✅ 环境设置成功")
        return DiverseEnhancedSceneEnv
        
    except Exception as e:
        print(f"❌ 环境设置失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_rendering_fix():
    """测试渲染修复"""
    print("🔧 测试渲染修复")
    print("=" * 60)
    
    DiverseEnhancedSceneEnv = setup_environment()
    if DiverseEnhancedSceneEnv is None:
        return False
    
    output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/rendering_fix_test")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 创建环境时使用特殊配置
        env = DiverseEnhancedSceneEnv(
            robot="google_robot_static",
            scene_name="dummy_tabletop",
            obs_mode="image",
            camera_cfgs={
                "add_segmentation": True,
                "width": 640,
                "height": 480,
            },
            render_mode="cameras",
            num_objects_range=(5, 8),
            # 添加渲染配置
            renderer_kwargs={
                "device": "cuda:0" if os.path.exists("/dev/nvidia0") else "cpu"
            }
        )
        
        print("✅ 环境创建成功")
        
        # 重置环境
        obs, info = env.reset()
        print("✅ 环境重置成功")
        
        # 检查观察结构
        print(f"📊 观察结构: {list(obs.keys())}")
        if 'image' in obs:
            print(f"📷 相机列表: {list(obs['image'].keys())}")
            
            for camera_name, camera_obs in obs['image'].items():
                print(f"📸 相机 {camera_name}:")
                print(f"   观察类型: {list(camera_obs.keys())}")
                
                if 'Color' in camera_obs:
                    image = camera_obs['Color']
                    print(f"   图像形状: {image.shape}")
                    print(f"   图像范围: {image.min():.3f} - {image.max():.3f}")
                    print(f"   图像均值: {image.mean():.3f}")
                    
                    # 检查图像是否全黑
                    if image.max() > 0.01:  # 如果有非零像素
                        print(f"   ✅ 图像正常")
                        
                        # 保存图像
                        image_path = output_dir / f"fixed_{camera_name}.png"
                        
                        # 确保图像在正确范围内
                        if image.max() <= 1.0:
                            image_save = (image * 255).astype(np.uint8)
                        else:
                            image_save = image.astype(np.uint8)
                        
                        cv2.imwrite(str(image_path), cv2.cvtColor(image_save, cv2.COLOR_RGB2BGR))
                        print(f"   💾 图像已保存: {image_path}")
                        
                    else:
                        print(f"   ❌ 图像全黑")
                        
                        # 尝试调试信息
                        print(f"   🔍 调试信息:")
                        print(f"      - 图像数据类型: {image.dtype}")
                        print(f"      - 图像唯一值数量: {len(np.unique(image))}")
                        
                        # 保存调试图像
                        debug_path = output_dir / f"debug_{camera_name}.png"
                        cv2.imwrite(str(debug_path), image)
                        print(f"   🐛 调试图像已保存: {debug_path}")
        
        # 获取物体信息
        if hasattr(env, 'all_scene_objects'):
            print(f"🎯 场景物体数量: {len(env.all_scene_objects)}")
            for i, obj in enumerate(env.all_scene_objects[:5]):  # 只显示前5个
                print(f"   {i+1}. {obj.name}")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"❌ 渲染测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def try_alternative_rendering():
    """尝试替代渲染方法"""
    print("\n🔄 尝试替代渲染方法")
    print("-" * 40)
    
    try:
        # 尝试使用不同的渲染配置
        import sapien.core as sapien
        
        # 创建场景
        engine = sapien.Engine()
        scene = engine.create_scene()
        
        # 添加光照
        scene.set_ambient_light([0.5, 0.5, 0.5])
        scene.add_directional_light([0, 1, -1], [0.5, 0.5, 0.5])
        
        # 添加地面
        scene.add_ground(altitude=0)
        
        # 添加一个简单的物体
        builder = scene.create_actor_builder()
        builder.add_box_collision(half_size=[0.05, 0.05, 0.05])
        builder.add_box_visual(half_size=[0.05, 0.05, 0.05], color=[1, 0, 0])
        box = builder.build(name="test_box")
        box.set_pose(sapien.Pose([0, 0, 0.05]))
        
        # 创建相机
        camera = scene.add_camera(
            name="test_camera",
            width=640,
            height=480,
            fovy=np.deg2rad(35),
            near=0.1,
            far=100,
        )
        camera.set_pose(sapien.Pose([1, 0, 1], [0.707, 0, 0, 0.707]))
        
        # 渲染
        scene.step()
        scene.update_render()
        camera.take_picture()
        
        # 获取图像
        color_rgba = camera.get_color_rgba()
        color_rgb = color_rgba[:, :, :3]
        
        print(f"✅ 替代渲染成功")
        print(f"   图像形状: {color_rgb.shape}")
        print(f"   图像范围: {color_rgb.min():.3f} - {color_rgb.max():.3f}")
        
        if color_rgb.max() > 0.01:
            output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/rendering_fix_test")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            image_path = output_dir / "alternative_render_test.png"
            image_save = (color_rgb * 255).astype(np.uint8)
            cv2.imwrite(str(image_path), cv2.cvtColor(image_save, cv2.COLOR_RGB2BGR))
            print(f"   💾 替代渲染图像已保存: {image_path}")
            return True
        else:
            print(f"   ❌ 替代渲染也是黑色")
            return False
            
    except Exception as e:
        print(f"❌ 替代渲染失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 SimplerEnv 渲染修复测试")
    print("=" * 60)
    
    # 测试主要渲染
    success1 = test_rendering_fix()
    
    # 测试替代渲染
    success2 = try_alternative_rendering()
    
    if success1 or success2:
        print(f"\n✅ 至少一种渲染方法成功")
    else:
        print(f"\n❌ 所有渲染方法都失败")
        print(f"💡 建议:")
        print(f"   1. 检查GPU驱动是否正确安装")
        print(f"   2. 尝试在有显示器的环境中运行")
        print(f"   3. 检查SAPIEN和ManiSkill2的安装")
    
    print("\n" + "=" * 60)
