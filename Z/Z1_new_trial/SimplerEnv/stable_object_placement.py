#!/usr/bin/env python3
"""
稳定的物体放置系统

使用更保守的策略确保物体稳定放置在桌面上，避免掉落。
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")

def test_stable_placement():
    """测试稳定的物体放置系统"""
    print("=" * 60)
    print("SimplerEnv稳定物体放置系统测试")
    print("=" * 60)
    
    try:
        from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
        import sapien.core as sapien
        from transforms3d.euler import euler2quat
        
        print("✓ 成功导入环境模块")
        
        # 测试场景配置
        test_scenarios = [
            {
                "name": "稳定8物体测试",
                "target": "eggplant",
                "distractors": ["opened_coke_can", "opened_pepsi_can", "green_cube_3cm", 
                              "yellow_cube_3cm", "apple", "orange", "bridge_carrot_generated_modified"],
                "description": "茄子 + 7个干扰物体 (稳定放置)"
            }
        ]
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n{'='*50}")
            print(f"场景 {i+1}: {scenario['name']}")
            print(f"描述: {scenario['description']}")
            print(f"{'='*50}")
            
            try:
                # 创建稳定的测试环境
                class StablePlacementEnv(GraspSingleCustomInSceneEnv):
                    def __init__(self, target_id, distractor_ids, **kwargs):
                        kwargs.setdefault("model_ids", [target_id])
                        kwargs.setdefault("distractor_model_ids", distractor_ids)
                        kwargs.setdefault("robot", "google_robot_static")
                        super().__init__(**kwargs)
                    
                    def reset(self, seed=None, options=None):
                        if options is None:
                            options = dict()
                        options = options.copy()
                        options["distractor_model_ids"] = self.distractor_model_ids
                        return super().reset(seed=seed, options=options)
                    
                    def _initialize_actors(self):
                        """稳定的物体初始化方法"""
                        if not hasattr(self, 'obj') or self.obj is None:
                            print("警告：没有目标物体")
                            return
                        
                        # 收集所有物体
                        all_objects = [self.obj]
                        if hasattr(self, 'distractor_objs') and self.distractor_objs:
                            all_objects.extend(self.distractor_objs)
                        
                        print(f"开始稳定初始化 {len(all_objects)} 个物体")
                        
                        # 使用更保守的工作空间边界 - 确保在桌面中心区域
                        workspace_bounds = ((-0.35, -0.15), (0.05, 0.35))  # 更小更安全的区域
                        min_spacing = 0.08  # 更小的间距，确保物体不重叠
                        
                        # 预定义安全位置 - 手动指定已知安全的位置
                        safe_positions = [
                            (-0.30, 0.10),  # 位置1
                            (-0.25, 0.10),  # 位置2
                            (-0.20, 0.10),  # 位置3
                            (-0.30, 0.20),  # 位置4
                            (-0.25, 0.20),  # 位置5
                            (-0.20, 0.20),  # 位置6
                            (-0.30, 0.30),  # 位置7
                            (-0.25, 0.30),  # 位置8
                        ]
                        
                        print(f"使用预定义的 {len(safe_positions)} 个安全位置")
                        
                        # 将机器人移到远处避免碰撞
                        self.agent.robot.set_pose(sapien.Pose([-10, 0, 0]))
                        
                        # 逐个放置物体，确保每个都稳定
                        for i, obj in enumerate(all_objects):
                            if obj is None:
                                print(f"警告：第{i}个物体为None，跳过")
                                continue
                            
                            # 使用预定义的安全位置
                            if i < len(safe_positions):
                                x, y = safe_positions[i]
                            else:
                                # 如果物体太多，使用随机位置但更保守
                                x = self._episode_rng.uniform(-0.30, -0.20)
                                y = self._episode_rng.uniform(0.10, 0.30)
                            
                            # 直接放在桌面上，不要从高处落下
                            z = self.scene_table_height + 0.02  # 只比桌面高一点点
                            
                            # 不使用随机旋转，保持物体稳定
                            q = euler2quat(0, 0, 0)  # 无旋转
                            
                            # 设置物体位置
                            print(f"稳定放置物体 {obj.name} 在位置: ({x:.2f}, {y:.2f}, {z:.2f})")
                            obj.set_pose(sapien.Pose([x, y, z], q))
                            
                            # 不锁定运动，让物体自然稳定
                            # 但设置阻尼减少运动
                            obj.set_damping(0.5, 0.5)  # 增加阻尼
                            
                            # 每放置一个物体就稳定一下
                            print(f"  稳定物体 {obj.name}...")
                            self._settle(0.3)
                            
                            # 检查物体是否稳定
                            pos_after = obj.pose.p
                            if pos_after[2] < self.scene_table_height - 0.05:
                                print(f"  ⚠ 警告: {obj.name} 位置不稳定，重新调整")
                                # 重新放置到更安全的位置
                                obj.set_pose(sapien.Pose([x, y, self.scene_table_height + 0.05], q))
                                obj.set_velocity(np.zeros(3))
                                obj.set_angular_velocity(np.zeros(3))
                                self._settle(0.5)
                        
                        # 最终全体稳定
                        print("最终稳定所有物体...")
                        self._settle(1.0)
                        
                        # 检查并修正任何不稳定的物体
                        print("检查并修正物体位置...")
                        for i, obj in enumerate(all_objects):
                            if obj is not None:
                                pos = obj.pose.p
                                if pos[2] < self.scene_table_height - 0.05:
                                    print(f"  修正掉落的物体: {obj.name}")
                                    # 强制放回桌面
                                    if i < len(safe_positions):
                                        x, y = safe_positions[i]
                                    else:
                                        x, y = -0.25, 0.20
                                    obj.set_pose(sapien.Pose([x, y, self.scene_table_height + 0.05], euler2quat(0, 0, 0)))
                                    obj.set_velocity(np.zeros(3))
                                    obj.set_angular_velocity(np.zeros(3))
                        
                        # 最终稳定
                        self._settle(0.5)
                        
                        # 最终检查物体位置
                        print("最终物体位置:")
                        for j, obj in enumerate(all_objects):
                            if obj is not None:
                                pos = obj.pose.p
                                status = "✓ 稳定" if pos[2] >= self.scene_table_height - 0.05 else "✗ 掉落"
                                print(f"  {j+1}. {obj.name}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f}) {status}")
                        
                        print("稳定物体初始化完成")
                
                # 创建环境
                env = StablePlacementEnv(
                    target_id=scenario["target"],
                    distractor_ids=scenario["distractors"],
                    obs_mode="image",
                    control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",
                    render_mode="rgb_array"
                )
                
                print(f"✓ 成功创建环境")
                
                # 重置环境
                obs, info = env.reset()
                print(f"✓ 成功重置环境")
                
                # 统计物体
                target_count = 1
                distractor_count = len(getattr(env.unwrapped, 'distractor_objs', []))
                total_objects = target_count + distractor_count
                
                print(f"物体统计:")
                print(f"  - 目标物体: {target_count} ({scenario['target']})")
                print(f"  - 干扰物体: {distractor_count}")
                print(f"  - 总物体数: {total_objects}")
                
                # 获取场景中的所有actors
                actors = env.unwrapped._scene.get_all_actors()
                object_actors = [a for a in actors if a.name != 'arena']
                print(f"  - 场景物体: {len(object_actors)}")
                
                # 检查物体位置
                table_height = env.unwrapped.scene_table_height
                on_table_count = 0
                below_table_count = 0
                
                print(f"物体位置检查 (桌面高度: {table_height:.2f}):")
                for j, actor in enumerate(object_actors):
                    pos = actor.pose.p
                    if pos[2] >= table_height - 0.05:  # 在桌面上或附近
                        on_table_count += 1
                        status = "✓ 在桌面上"
                    else:
                        below_table_count += 1
                        status = "✗ 掉落了"
                    print(f"  {j+1}. {actor.name}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f}) {status}")
                
                print(f"位置统计: 桌面上={on_table_count}, 掉落={below_table_count}")
                
                # 保存相机图像
                if 'image' in obs:
                    for camera_name in obs['image'].keys():
                        camera_data = obs['image'][camera_name]
                        
                        # 获取图像数据
                        rgb_img = None
                        for img_key in ['Color', 'rgb', 'color']:
                            if img_key in camera_data:
                                rgb_img = camera_data[img_key]
                                break
                        
                        if rgb_img is not None:
                            # 确保图像格式正确
                            if rgb_img.dtype != np.uint8:
                                rgb_img = (rgb_img * 255).astype(np.uint8)
                            
                            output_path = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/stable_placement_{scenario['name']}_{camera_name}.png"
                            cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                            print(f"  - 保存{camera_name}图像: {output_path}")
                
                env.close()
                print(f"✓ {scenario['name']} 测试完成")
                
                # 返回结果
                success = below_table_count == 0
                return success, on_table_count, below_table_count
                
            except Exception as e:
                print(f"✗ {scenario['name']} 测试失败: {e}")
                import traceback
                traceback.print_exc()
                return False, 0, 0
        
    except Exception as e:
        print(f"\n✗ 稳定物体放置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 0


if __name__ == "__main__":
    print("SimplerEnv稳定物体放置系统测试")
    success, on_table, below_table = test_stable_placement()
    
    if success:
        print(f"\n🎉 稳定物体放置测试成功！")
        print(f"   - 所有 {on_table} 个物体都稳定地放置在桌面上")
        print(f"   - 没有物体掉落")
    else:
        print(f"\n⚠ 稳定物体放置测试结果:")
        print(f"   - 桌面上: {on_table} 个物体")
        print(f"   - 掉落: {below_table} 个物体")
        if on_table > below_table:
            print(f"   - 大部分物体成功放置，这是一个改进！")
        else:
            print(f"   - 需要进一步调整放置策略")
