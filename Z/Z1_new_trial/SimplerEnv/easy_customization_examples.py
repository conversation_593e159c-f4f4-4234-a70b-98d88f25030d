#!/usr/bin/env python3
"""
SimplerEnv简单定制示例

展示如何直观地修改各种参数来定制您的场景
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from simpler_env_interface import (
    create_scene_simple,
    create_scene_custom,
    create_scene_with_custom_positions,
    generate_grid_positions,
    generate_circle_positions
)

def example_1_change_objects():
    """示例1: 修改物体种类和数量"""
    print("🎯 示例1: 修改物体种类和数量")
    print("=" * 50)
    
    # 方法1: 使用简单接口，指定物体
    print("方法1: 简单接口指定物体")
    success, results, images = create_scene_simple(
        preset="desktop_picking",
        objects=["apple", "coke_can"],  # 🔧 在这里修改物体！
        num_episodes=1,
        verbose=True
    )
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 方法2: 使用中级接口，完全自定义
    print("\n方法2: 中级接口完全自定义")
    success, results, images = create_scene_custom(
        robot_type="google_robot_static",
        camera_type="overhead_camera", 
        lighting_mode="laboratory",
        objects=["orange", "green_cube_3cm", "yellow_cube_3cm"],  # 🔧 在这里修改物体！
        num_episodes=1,
        verbose=True
    )
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")

def example_2_change_positions():
    """示例2: 修改物体位置"""
    print("\n🎯 示例2: 修改物体位置")
    print("=" * 50)
    
    # 方法1: 手动指定精确位置
    print("方法1: 手动指定精确位置")
    objects = ["apple", "orange"]
    
    # 🔧 在这里修改位置坐标！
    custom_positions = [
        (-0.25, 0.15, 0),    # apple的位置 (x, y, z)
        (-0.20, 0.20, 0),    # orange的位置 (x, y, z)
    ]
    
    success, results, images = create_scene_with_custom_positions(
        objects=objects,
        positions=custom_positions,
        image_prefix="custom_pos_demo",
        num_episodes=1,
        verbose=True
    )
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 方法2: 使用网格布局
    print("\n方法2: 使用网格布局")
    objects = ["apple", "orange", "coke_can"]
    
    # 🔧 在这里修改网格参数！
    grid_positions = generate_grid_positions(
        num_objects=3,
        workspace_bounds=((-0.35, -0.15), (0.05, 0.35)),  # 工作空间大小
        z_height=0  # 高度（0表示自动）
    )
    
    success, results, images = create_scene_with_custom_positions(
        objects=objects,
        positions=grid_positions,
        image_prefix="grid_demo",
        num_episodes=1,
        verbose=True
    )
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")

def example_3_change_lighting():
    """示例3: 修改光照环境"""
    print("\n🎯 示例3: 修改光照环境")
    print("=" * 50)
    
    # 测试不同光照模式
    lighting_modes = [
        ("indoor_bright", "明亮室内"),
        ("laboratory", "实验室标准"),
        ("natural", "自然光照"),
        ("industrial", "工业环境")
    ]
    
    for mode, description in lighting_modes:
        print(f"\n测试光照: {description} ({mode})")
        
        success, results, images = create_scene_custom(
            robot_type="google_robot_static",
            camera_type="overhead_camera",
            lighting_mode=mode,  # 🔧 在这里修改光照模式！
            objects=["apple", "orange"],
            image_prefix=f"lighting_{mode}",
            num_episodes=1,
            verbose=False  # 简化输出
        )
        print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")

def example_4_change_robot_camera():
    """示例4: 修改机器人和相机"""
    print("\n🎯 示例4: 修改机器人和相机")
    print("=" * 50)
    
    # 测试不同机器人和相机组合
    combinations = [
        ("google_robot_static", "overhead_camera", "推荐组合"),
        ("google_robot_static", "base_camera", "侧面视角"),
        ("widowx", "base_camera", "真实机器人")
    ]
    
    for robot, camera, description in combinations:
        print(f"\n测试组合: {description}")
        print(f"   机器人: {robot}")
        print(f"   相机: {camera}")
        
        success, results, images = create_scene_custom(
            robot_type=robot,      # 🔧 在这里修改机器人！
            camera_type=camera,    # 🔧 在这里修改相机！
            lighting_mode="laboratory",
            objects=["apple", "orange"],
            image_prefix=f"robot_{robot}_{camera}",
            num_episodes=1,
            verbose=False
        )
        print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")

def example_5_change_workspace():
    """示例5: 修改工作空间"""
    print("\n🎯 示例5: 修改工作空间大小")
    print("=" * 50)
    
    # 测试不同工作空间大小
    workspaces = {
        "紧凑型": ((-0.28, -0.22), (0.12, 0.18)),
        "标准型": ((-0.30, -0.20), (0.10, 0.20)),
        "扩展型": ((-0.35, -0.15), (0.05, 0.35))
    }
    
    for name, bounds in workspaces.items():
        print(f"\n测试工作空间: {name}")
        print(f"   X范围: {bounds[0]}")
        print(f"   Y范围: {bounds[1]}")
        
        success, results, images = create_scene_custom(
            robot_type="google_robot_static",
            camera_type="overhead_camera",
            lighting_mode="laboratory",
            objects=["apple", "orange"],
            workspace_bounds=bounds,  # 🔧 在这里修改工作空间！
            image_prefix=f"workspace_{name}",
            num_episodes=1,
            verbose=False
        )
        print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")

def quick_modification_guide():
    """快速修改指南"""
    print("\n🔧 快速修改指南")
    print("=" * 50)
    
    print("要修改参数，只需要在函数调用中更改对应的值：")
    print()
    
    print("1️⃣ 修改物体:")
    print("   objects=['apple', 'orange', 'coke_can']  # 改成您想要的物体")
    print()
    
    print("2️⃣ 修改位置:")
    print("   positions=[(-0.25, 0.15, 0), (-0.22, 0.18, 0)]  # 改成您想要的坐标")
    print()
    
    print("3️⃣ 修改光照:")
    print("   lighting_mode='laboratory'  # 可选: indoor_bright, natural, industrial")
    print()
    
    print("4️⃣ 修改机器人:")
    print("   robot_type='google_robot_static'  # 可选: google_robot_mobile, widowx")
    print()
    
    print("5️⃣ 修改相机:")
    print("   camera_type='overhead_camera'  # 可选: base_camera")
    print()
    
    print("6️⃣ 修改工作空间:")
    print("   workspace_bounds=((-0.30, -0.20), (0.10, 0.20))  # ((x_min, x_max), (y_min, y_max))")
    print()
    
    print("7️⃣ 修改场景数量:")
    print("   num_episodes=3  # 生成3个场景")
    print()
    
    print("8️⃣ 修改输出文件名:")
    print("   image_prefix='my_scene'  # 图像文件前缀")

def main():
    """主函数 - 运行所有示例"""
    print("🎨 SimplerEnv简单定制示例")
    print("=" * 60)
    print("本示例展示如何直观地修改各种参数")
    print()
    
    # 运行示例（注释掉不需要的示例以节省时间）
    example_1_change_objects()
    # example_2_change_positions()
    # example_3_change_lighting()
    # example_4_change_robot_camera()
    # example_5_change_workspace()
    
    quick_modification_guide()
    
    print("\n🎉 定制示例完成！")
    print("💡 提示:")
    print("   • 取消注释上面的示例函数来运行更多测试")
    print("   • 直接修改函数中的参数值来定制您的场景")
    print("   • 建议从简单的2个物体开始测试")

if __name__ == "__main__":
    main()
