"""
场景多样性增强模块

这个模块提供了各种增强训练数据多样性的功能，包括：
- 光照变化
- 背景变化  
- 物体材质变化
- 物理属性随机化
- 相机视角变化
"""

import numpy as np
import sapien.core as sapien
from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
import random


class LightingVariationType(Enum):
    """光照变化类型"""
    AMBIENT_ONLY = "ambient_only"
    DIRECTIONAL_ONLY = "directional_only"
    MIXED = "mixed"
    DRAMATIC = "dramatic"
    SOFT = "soft"
    COLORED = "colored"


class MaterialVariationType(Enum):
    """材质变化类型"""
    FRICTION = "friction"
    RESTITUTION = "restitution"
    DENSITY = "density"
    COLOR = "color"
    TEXTURE = "texture"


class DiversityEnhancer:
    """多样性增强器"""
    
    def __init__(self, seed: Optional[int] = None,
                 material_intensity: str = "medium",
                 lighting_intensity: str = "medium",
                 custom_params: Optional[Dict] = None):
        """
        初始化多样性增强器

        Args:
            seed: 随机种子
            material_intensity: 材质变化强度 ("subtle", "medium", "dramatic")
            lighting_intensity: 光照变化强度 ("subtle", "medium", "dramatic")
            custom_params: 自定义参数字典
        """
        self.rng = np.random.RandomState(seed)
        self.material_intensity = material_intensity
        self.lighting_intensity = lighting_intensity

        # 根据强度设置光照变化参数
        self.lighting_params = self._get_lighting_params(lighting_intensity)

        # 根据强度设置材质变化参数
        self.material_params = self._get_material_params(material_intensity)

        # 初始化物理属性变化参数 (基于SAPIEN官方API验证)
        self.physics_params = {
            "gravity_range": [8.0, 12.0],  # 重力变化范围 - 通过scene.set_gravity()
            "linear_damping_range": [0.0, 0.5],  # 线性阻尼 - 通过actor.set_damping()
            "angular_damping_range": [0.0, 0.5],  # 角阻尼 - 通过actor.set_damping()
            "contact_offset_range": [0.001, 0.01]  # 接触偏移 - 可能不支持新版SAPIEN
        }

        # 应用自定义参数
        if custom_params:
            self._apply_custom_params(custom_params)

    def _get_lighting_params(self, intensity: str) -> Dict:
        """根据强度获取光照参数"""
        if intensity == "subtle":
            return {
                "ambient_range": [(0.2, 0.2, 0.2), (0.4, 0.4, 0.4)],
                "directional_intensity_range": [0.5, 1.5],
                "directional_color_range": [(0.9, 0.9, 0.9), (1.1, 1.1, 1.1)],
                "shadow_probability": 0.3,
                "colored_light_probability": 0.1
            }
        elif intensity == "dramatic":
            return {
                "ambient_range": [(0.0, 0.0, 0.0), (0.8, 0.8, 0.8)],
                "directional_intensity_range": [0.1, 5.0],
                "directional_color_range": [(0.5, 0.5, 0.5), (2.0, 2.0, 2.0)],
                "shadow_probability": 0.9,
                "colored_light_probability": 0.4
            }
        else:  # medium
            return {
                "ambient_range": [(0.1, 0.1, 0.1), (0.6, 0.6, 0.6)],
                "directional_intensity_range": [0.3, 3.0],
                "directional_color_range": [(0.8, 0.8, 0.8), (1.2, 1.2, 1.2)],
                "shadow_probability": 0.7,
                "colored_light_probability": 0.2
            }

    def _get_material_params(self, intensity: str) -> Dict:
        """根据强度获取材质参数"""
        if intensity == "subtle":
            return {
                "friction_range": [0.5, 1.5],
                "restitution_range": [0.1, 0.4],
                "density_range": [0.7, 1.5],
                "color_variations": [
                    (1.0, 0.95, 0.95),  # 极淡红
                    (0.95, 1.0, 0.95),  # 极淡绿
                    (0.95, 0.95, 1.0),  # 极淡蓝
                    (1.0, 1.0, 0.95),   # 极淡黄
                ],
                "metallic_range": [0.0, 0.2],
                "roughness_range": [0.3, 0.8]
            }
        elif intensity == "dramatic":
            return {
                "friction_range": [0.05, 5.0],
                "restitution_range": [0.0, 0.95],
                "density_range": [0.1, 5.0],
                "color_variations": [
                    (2.0, 0.2, 0.2),    # 鲜红
                    (0.2, 2.0, 0.2),    # 鲜绿
                    (0.2, 0.2, 2.0),    # 鲜蓝
                    (2.0, 2.0, 0.2),    # 鲜黄
                    (2.0, 0.2, 2.0),    # 鲜紫
                    (0.2, 2.0, 2.0),    # 鲜青
                    (0.1, 0.1, 0.1),    # 深黑
                    (1.5, 1.5, 1.5),    # 亮白
                ],
                "metallic_range": [0.0, 1.0],
                "roughness_range": [0.0, 1.0]
            }
        else:  # medium
            return {
                "friction_range": [0.1, 2.0],
                "restitution_range": [0.0, 0.8],
                "density_range": [0.3, 3.0],
                "color_variations": [
                    (1.5, 0.6, 0.6),    # 明显红
                    (0.6, 1.5, 0.6),    # 明显绿
                    (0.6, 0.6, 1.5),    # 明显蓝
                    (1.5, 1.5, 0.6),    # 明显黄
                    (1.5, 0.6, 1.5),    # 明显紫
                    (0.6, 1.5, 1.5),    # 明显青
                ],
                "metallic_range": [0.0, 0.5],
                "roughness_range": [0.1, 0.9]
            }

    def _apply_custom_params(self, custom_params: Dict):
        """应用自定义参数"""
        if "lighting" in custom_params:
            self.lighting_params.update(custom_params["lighting"])
        if "material" in custom_params:
            self.material_params.update(custom_params["material"])
        if "physics" in custom_params:
            self.physics_params.update(custom_params["physics"])
        
        # 相机变化参数
        self.camera_params = {
            "position_noise_range": 0.05,  # 位置噪声范围
            "rotation_noise_range": 0.1,   # 旋转噪声范围
            "fov_range": [45, 75],          # 视野角度范围
            "exposure_range": [0.8, 1.2]   # 曝光范围
        }
    
    def apply_lighting_variation(self, scene: sapien.Scene, variation_type: LightingVariationType = None) -> Dict[str, Any]:
        """应用光照变化"""
        if variation_type is None:
            # 修复: 将枚举列表转换为numpy数组，然后选择
            lighting_types = list(LightingVariationType)
            variation_type = lighting_types[self.rng.randint(0, len(lighting_types))]
        
        # 清除现有光照
        scene.set_ambient_light([0, 0, 0])
        
        lighting_info = {"type": variation_type.value, "lights": []}
        
        if variation_type == LightingVariationType.AMBIENT_ONLY:
            # 仅环境光
            ambient = self._random_color_in_range(*self.lighting_params["ambient_range"])
            scene.set_ambient_light(ambient)
            lighting_info["ambient"] = ambient
            
        elif variation_type == LightingVariationType.DIRECTIONAL_ONLY:
            # 仅方向光 - 限制数量避免过载
            scene.set_ambient_light([0.05, 0.05, 0.05])  # 微弱环境光
            self._add_random_directional_lights(scene, lighting_info, num_lights=self.rng.randint(1, 2))

        elif variation_type == LightingVariationType.MIXED:
            # 混合光照 - 限制数量避免过载
            ambient = self._random_color_in_range([0.1, 0.1, 0.1], [0.4, 0.4, 0.4])
            scene.set_ambient_light(ambient)
            lighting_info["ambient"] = ambient
            self._add_random_directional_lights(scene, lighting_info, num_lights=self.rng.randint(1, 2))
            
        elif variation_type == LightingVariationType.DRAMATIC:
            # 戏剧性光照
            scene.set_ambient_light([0.02, 0.02, 0.02])  # 很暗的环境光
            self._add_dramatic_lighting(scene, lighting_info)
            
        elif variation_type == LightingVariationType.SOFT:
            # 柔和光照
            ambient = self._random_color_in_range([0.3, 0.3, 0.3], [0.5, 0.5, 0.5])
            scene.set_ambient_light(ambient)
            lighting_info["ambient"] = ambient
            self._add_soft_lighting(scene, lighting_info)
            
        elif variation_type == LightingVariationType.COLORED:
            # 彩色光照
            self._add_colored_lighting(scene, lighting_info)
        
        return lighting_info
    
    def _random_color_in_range(self, min_color: Tuple[float, float, float], 
                              max_color: Tuple[float, float, float]) -> List[float]:
        """在指定范围内生成随机颜色"""
        return [
            self.rng.uniform(min_color[i], max_color[i]) for i in range(3)
        ]
    
    def _add_random_directional_lights(self, scene: sapien.Scene, lighting_info: Dict, num_lights: int):
        """添加随机方向光"""
        shadow_count = 0  # 跟踪阴影光源数量
        max_shadow_lights = 1  # 最多只允许1个阴影光源

        for i in range(num_lights):
            # 随机方向
            direction = self.rng.uniform(-1, 1, size=3)
            direction = direction / np.linalg.norm(direction)

            # 随机强度和颜色
            intensity = self.rng.uniform(*self.lighting_params["directional_intensity_range"])
            color = self._random_color_in_range(*self.lighting_params["directional_color_range"])
            color = [c * intensity for c in color]

            # 限制阴影光源数量
            enable_shadow = (shadow_count < max_shadow_lights and
                           self.rng.random() < self.lighting_params["shadow_probability"])
            if enable_shadow:
                shadow_count += 1

            try:
                scene.add_directional_light(
                    direction.tolist(),
                    color,
                    shadow=enable_shadow,
                    scale=5,
                    shadow_map_size=1024 if enable_shadow else 0
                )

                lighting_info["lights"].append({
                    "type": "directional",
                    "direction": direction.tolist(),
                    "color": color,
                    "shadow": enable_shadow
                })
            except Exception as e:
                print(f"添加方向光失败: {e}")
                continue
    
    def _add_dramatic_lighting(self, scene: sapien.Scene, lighting_info: Dict):
        """添加戏剧性光照"""
        # 主光源 - 强烈的单一方向光
        # 修复: 使用索引选择避免维度问题
        directions = [
            [0, 0, -1],      # 顶部
            [1, 1, -1],      # 斜上方
            [-1, 0.5, -1],   # 侧面
        ]
        main_direction = directions[self.rng.randint(0, len(directions))]
        main_color = [3.0, 3.0, 3.0]
        
        try:
            scene.add_directional_light(
                main_direction, main_color, shadow=True, scale=5, shadow_map_size=2048
            )
            lighting_info["lights"].append({
                "type": "dramatic_main", "direction": main_direction, "color": main_color
            })
        except Exception as e:
            print(f"添加戏剧性主光源失败: {e}")
            # 降级为无阴影光源
            try:
                scene.add_directional_light(main_direction, main_color, shadow=False)
                lighting_info["lights"].append({
                    "type": "dramatic_main_no_shadow", "direction": main_direction, "color": main_color
                })
            except Exception as e2:
                print(f"添加无阴影主光源也失败: {e2}")

        # 补光 - 微弱的反向光
        fill_direction = [-d for d in main_direction]
        fill_color = [0.2, 0.2, 0.2]

        try:
            scene.add_directional_light(fill_direction, fill_color, shadow=False)
            lighting_info["lights"].append({
                "type": "dramatic_fill", "direction": fill_direction, "color": fill_color
            })
        except Exception as e:
            print(f"添加补光失败: {e}")
    
    def _add_soft_lighting(self, scene: sapien.Scene, lighting_info: Dict):
        """添加柔和光照"""
        # 多个弱光源创造柔和效果
        directions = [
            [0, 0, -1],
            [1, 0, -0.5],
            [-1, 0, -0.5],
            [0, 1, -0.5],
            [0, -1, -0.5]
        ]
        
        for direction in directions:
            intensity = self.rng.uniform(0.3, 0.8)
            color = [intensity] * 3
            
            scene.add_directional_light(direction, color, shadow=False)
            
            lighting_info["lights"].append({
                "type": "soft",
                "direction": direction,
                "color": color
            })
    
    def _add_colored_lighting(self, scene: sapien.Scene, lighting_info: Dict):
        """添加彩色光照"""
        scene.set_ambient_light([0.1, 0.1, 0.1])
        
        # 彩色主光源
        colors = [
            [2.0, 1.5, 1.0],  # 暖色
            [1.0, 1.5, 2.0],  # 冷色
            [2.0, 1.0, 1.5],  # 洋红
            [1.5, 2.0, 1.0],  # 绿色调
        ]
        
        # 修复: 使用索引选择避免维度问题
        main_color = colors[self.rng.randint(0, len(colors))]
        main_direction = [0, 0, -1]
        
        try:
            scene.add_directional_light(main_direction, main_color, shadow=True, scale=5)
            lighting_info["lights"].append({
                "type": "colored_main", "direction": main_direction, "color": main_color
            })
        except Exception as e:
            print(f"添加彩色主光源失败: {e}")
            # 降级为无阴影光源
            try:
                scene.add_directional_light(main_direction, main_color, shadow=False)
                lighting_info["lights"].append({
                    "type": "colored_main_no_shadow", "direction": main_direction, "color": main_color
                })
            except Exception as e2:
                print(f"添加无阴影彩色光源也失败: {e2}")

        # 补色光源（无阴影）
        complement_color = [2.0 - c for c in main_color]
        complement_direction = [1, 1, -1]

        try:
            scene.add_directional_light(complement_direction, complement_color, shadow=False)
            lighting_info["lights"].append({
                "type": "colored_complement", "direction": complement_direction, "color": complement_color
            })
        except Exception as e:
            print(f"添加补色光源失败: {e}")
    
    def apply_material_variation(self, actor: sapien.Actor, variation_types: List[MaterialVariationType] = None) -> Dict[str, Any]:
        """应用材质变化"""
        if variation_types is None:
            # 修复: 正确处理枚举类型的随机选择
            material_types = list(MaterialVariationType)
            num_variations = self.rng.randint(1, min(4, len(material_types) + 1))
            # 使用numpy的随机索引来选择枚举值
            selected_indices = self.rng.choice(len(material_types),
                                             size=num_variations,
                                             replace=False)
            variation_types = [material_types[i] for i in selected_indices]
        
        material_info = {"variations": []}
        
        for variation_type in variation_types:
            if variation_type == MaterialVariationType.FRICTION:
                self._apply_friction_variation(actor, material_info)
            elif variation_type == MaterialVariationType.RESTITUTION:
                self._apply_restitution_variation(actor, material_info)
            elif variation_type == MaterialVariationType.DENSITY:
                self._apply_density_variation(actor, material_info)
            elif variation_type == MaterialVariationType.COLOR:
                self._apply_color_variation(actor, material_info)
        
        return material_info
    
    def _apply_friction_variation(self, actor: sapien.Actor, material_info: Dict):
        """应用摩擦力变化"""
        friction_mult = self.rng.uniform(*self.material_params["friction_range"])
        
        # 获取现有材质属性
        shapes = actor.get_collision_shapes()
        if shapes:
            current_material = shapes[0].get_physical_material()
            new_static_friction = current_material.static_friction * friction_mult
            new_dynamic_friction = current_material.dynamic_friction * friction_mult
            
            # 创建新材质
            scene = actor.get_scene()
            new_material = scene.create_physical_material(
                static_friction=new_static_friction,
                dynamic_friction=new_dynamic_friction,
                restitution=current_material.restitution
            )
            
            # 应用到所有形状
            for shape in shapes:
                shape.set_physical_material(new_material)
            
            material_info["variations"].append({
                "type": "friction",
                "multiplier": friction_mult,
                "static_friction": new_static_friction,
                "dynamic_friction": new_dynamic_friction
            })
    
    def _apply_restitution_variation(self, actor: sapien.Actor, material_info: Dict):
        """应用弹性变化"""
        restitution = self.rng.uniform(*self.material_params["restitution_range"])
        
        shapes = actor.get_collision_shapes()
        if shapes:
            current_material = shapes[0].get_physical_material()
            
            scene = actor.get_scene()
            new_material = scene.create_physical_material(
                static_friction=current_material.static_friction,
                dynamic_friction=current_material.dynamic_friction,
                restitution=restitution
            )
            
            for shape in shapes:
                shape.set_physical_material(new_material)
            
            material_info["variations"].append({
                "type": "restitution",
                "value": restitution
            })
    
    def _apply_density_variation(self, actor: sapien.Actor, material_info: Dict):
        """应用密度变化"""
        density_mult = self.rng.uniform(*self.material_params["density_range"])
        
        # 注意：SAPIEN中密度变化需要重新创建actor，这里记录变化信息
        material_info["variations"].append({
            "type": "density",
            "multiplier": density_mult
        })
    
    def _apply_color_variation(self, actor: sapien.Actor, material_info: Dict):
        """应用颜色变化"""
        # 修复: 使用索引选择避免维度问题
        color_variations = self.material_params["color_variations"]
        color_variation = color_variations[self.rng.randint(0, len(color_variations))]

        # 获取材质属性范围
        metallic_range = self.material_params.get("metallic_range", [0.0, 0.3])
        roughness_range = self.material_params.get("roughness_range", [0.2, 0.8])

        # 随机生成材质属性
        metallic = self.rng.uniform(*metallic_range)
        roughness = self.rng.uniform(*roughness_range)

        # 获取视觉形状并应用颜色变化
        visual_bodies = actor.get_visual_bodies()
        applied_count = 0

        for body in visual_bodies:
            for shape in body.get_render_shapes():
                material = shape.material
                if material:
                    try:
                        # 确保颜色是正确的格式 (R, G, B, A)
                        if isinstance(color_variation, (list, tuple)) and len(color_variation) == 3:
                            rgba_color = list(color_variation) + [1.0]  # 添加alpha通道
                        else:
                            rgba_color = [1.0, 1.0, 1.0, 1.0]  # 默认白色

                        # 应用颜色调制
                        material.set_base_color(rgba_color)

                        # 应用材质属性（如果支持）
                        try:
                            if hasattr(material, 'set_metallic'):
                                material.set_metallic(metallic)
                            if hasattr(material, 'set_roughness'):
                                material.set_roughness(roughness)
                            if hasattr(material, 'set_specular'):
                                material.set_specular(1.0 - roughness)  # 镜面反射与粗糙度相反
                        except:
                            pass  # 如果不支持这些属性，跳过

                        applied_count += 1

                    except Exception as e:
                        print(f"颜色变化应用失败: {e}")
                        continue

        material_info["variations"].append({
            "type": "color",
            "color": color_variation,
            "metallic": metallic,
            "roughness": roughness,
            "applied_shapes": applied_count
        })
    
    def apply_physics_variation(self, scene: sapien.Scene) -> Dict[str, Any]:
        """
        应用物理属性变化 (基于SAPIEN官方API验证)

        Args:
            scene: SAPIEN场景对象

        Returns:
            Dict: 应用的物理变化信息
        """
        physics_info = {}

        # 1. 重力变化 - ✅ 通过scene.set_gravity()确认支持
        gravity = self.rng.uniform(*self.physics_params["gravity_range"])
        try:
            scene.set_gravity([0, 0, -gravity])
            physics_info["gravity"] = gravity
            physics_info["gravity_applied"] = True
        except AttributeError:
            # 新版本SAPIEN可能使用不同的方法名
            try:
                scene.gravity = [0, 0, -gravity]
                physics_info["gravity"] = gravity
                physics_info["gravity_applied"] = True
            except:
                physics_info["gravity"] = "not_supported"
                physics_info["gravity_applied"] = False

        # 2. 接触参数变化 - ❓ 可能在新版SAPIEN中不支持
        try:
            contact_offset = self.rng.uniform(*self.physics_params["contact_offset_range"])
            scene_config = scene.get_scene_config()
            scene_config.contact_offset = contact_offset
            physics_info["contact_offset"] = contact_offset
            physics_info["contact_offset_applied"] = True
        except AttributeError:
            physics_info["contact_offset"] = "not_supported"
            physics_info["contact_offset_applied"] = False

        # 3. 生成阻尼参数供后续应用到actor - ✅ 通过actor.set_damping()确认支持
        linear_damping = self.rng.uniform(*self.physics_params["linear_damping_range"])
        angular_damping = self.rng.uniform(*self.physics_params["angular_damping_range"])

        physics_info["linear_damping"] = linear_damping
        physics_info["angular_damping"] = angular_damping
        physics_info["damping_note"] = "需要在创建actor后调用apply_damping_to_actors()方法应用"

        return physics_info

    def apply_damping_to_actors(self, actors: List[sapien.Actor], physics_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        将阻尼参数应用到指定的actors上

        Args:
            actors: 要应用阻尼的actor列表
            physics_info: 包含阻尼参数的物理信息字典

        Returns:
            Dict: 阻尼应用结果信息
        """
        damping_info = {"applied_actors": 0, "failed_actors": 0}

        if "linear_damping" not in physics_info or "angular_damping" not in physics_info:
            damping_info["error"] = "physics_info中缺少阻尼参数"
            return damping_info

        linear_damping = physics_info["linear_damping"]
        angular_damping = physics_info["angular_damping"]

        for actor in actors:
            try:
                # ✅ 通过actor.set_damping()应用阻尼 - SAPIEN官方API
                actor.set_damping(linear=linear_damping, angular=angular_damping)
                damping_info["applied_actors"] += 1
            except Exception as e:
                damping_info["failed_actors"] += 1
                if "errors" not in damping_info:
                    damping_info["errors"] = []
                damping_info["errors"].append(f"Actor {actor.name}: {str(e)}")

        damping_info["linear_damping"] = linear_damping
        damping_info["angular_damping"] = angular_damping

        return damping_info
    
    def apply_camera_variation(self, camera_config: Dict) -> Dict[str, Any]:
        """应用相机变化"""
        camera_info = {"original": camera_config.copy()}
        
        # 位置噪声
        if "position" in camera_config:
            noise = self.rng.uniform(
                -self.camera_params["position_noise_range"],
                self.camera_params["position_noise_range"],
                3
            )
            camera_config["position"] = [
                camera_config["position"][i] + noise[i] for i in range(3)
            ]
        
        # 旋转噪声
        if "rotation" in camera_config:
            rot_noise = self.rng.uniform(
                -self.camera_params["rotation_noise_range"],
                self.camera_params["rotation_noise_range"],
                3
            )
            camera_config["rotation"] = [
                camera_config["rotation"][i] + rot_noise[i] for i in range(3)
            ]
        
        # 视野角度变化
        if "fov" in camera_config:
            camera_config["fov"] = self.rng.uniform(*self.camera_params["fov_range"])
        
        camera_info["modified"] = camera_config.copy()
        return camera_info
