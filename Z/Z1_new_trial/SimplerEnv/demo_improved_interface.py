#!/usr/bin/env python3
"""
SimplerEnv改进接口完整演示

展示所有新功能的使用方法
"""

import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))

from simpler_env_interface import (
    create_scene_simple,
    create_scene_custom,
    create_scene_with_custom_positions,
    generate_grid_positions,
    generate_circle_positions,
    SceneConfigManager,
    AVAILABLE_OBJECTS,
    LIGHTING_CONFIGS
)

def demo_simple_interface():
    """演示简单接口"""
    print("🚀 演示1: 简单接口 - 一键生成")
    print("=" * 50)
    print("适合新手用户，只需选择预设配置即可")
    print()
    
    # 列出可用预设
    presets = SceneConfigManager.list_presets()
    print("可用预设配置:")
    for preset, desc in presets.items():
        print(f"  • {preset}: {desc}")
    print()
    
    # 使用简单接口
    print("使用桌面抓取预设生成场景...")
    success, results, images = create_scene_simple(
        preset="desktop_picking",
        num_episodes=1,
        verbose=True
    )
    
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    return success

def demo_custom_interface():
    """演示中级接口"""
    print("\n🎛️ 演示2: 中级接口 - 自定义配置")
    print("=" * 50)
    print("适合有经验用户，可以自定义机器人、相机、光照等")
    print()
    
    # 展示配置选项
    print("可用光照模式:")
    for mode, config in LIGHTING_CONFIGS.items():
        print(f"  • {mode}: {config['description']}")
    print()
    
    print("使用自定义配置生成场景...")
    success, results, images = create_scene_custom(
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="laboratory",
        objects=["apple", "orange"],  # 简化为2个物体
        num_episodes=1,
        verbose=True
    )
    
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    return success

def demo_custom_positions():
    """演示自定义位置接口"""
    print("\n🎯 演示3: 自定义位置接口 - 精确控制")
    print("=" * 50)
    print("适合需要精确控制物体位置的场景")
    print()
    
    # 手动指定位置
    objects = ["apple", "orange"]
    positions = [
        (-0.25, 0.15, 0),    # apple在中心
        (-0.22, 0.18, 0),    # orange在右上
    ]
    
    print("手动指定位置:")
    for i, (obj, pos) in enumerate(zip(objects, positions)):
        print(f"  {obj}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f})")
    print()
    
    success, results, images = create_scene_with_custom_positions(
        objects=objects,
        positions=positions,
        image_prefix="demo_custom_pos",
        num_episodes=1,
        verbose=True
    )
    
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    return success

def demo_layout_generators():
    """演示布局生成器"""
    print("\n📐 演示4: 布局生成器 - 自动生成位置")
    print("=" * 50)
    print("自动生成规整的物体布局")
    print()
    
    # 网格布局
    print("网格布局生成:")
    grid_positions = generate_grid_positions(3)
    for i, pos in enumerate(grid_positions):
        print(f"  位置{i+1}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f})")
    
    success1, _, _ = create_scene_with_custom_positions(
        objects=["apple", "orange", "coke_can"],
        positions=grid_positions,
        image_prefix="demo_grid",
        num_episodes=1,
        verbose=False
    )
    print(f"网格布局测试: {'✅ 成功' if success1 else '❌ 失败'}")
    
    # 圆形布局
    print("\n圆形布局生成:")
    circle_positions = generate_circle_positions(2, radius=0.05)
    for i, pos in enumerate(circle_positions):
        print(f"  位置{i+1}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f})")
    
    success2, _, _ = create_scene_with_custom_positions(
        objects=["apple", "orange"],
        positions=circle_positions,
        image_prefix="demo_circle",
        num_episodes=1,
        verbose=False
    )
    print(f"圆形布局测试: {'✅ 成功' if success2 else '❌ 失败'}")
    
    return success1 and success2

def show_available_options():
    """显示所有可用选项"""
    print("\n📋 可用配置选项总览")
    print("=" * 50)
    
    print("🎯 预设配置:")
    presets = SceneConfigManager.list_presets()
    for preset, desc in presets.items():
        print(f"   • {preset}: {desc}")
    
    print("\n🔆 光照模式:")
    for mode, config in LIGHTING_CONFIGS.items():
        print(f"   • {mode}: {config['description']}")
    
    print("\n🎲 可用物体:")
    for obj, info in AVAILABLE_OBJECTS.items():
        print(f"   • {obj} ({info['category']}, {info['size']}, {info['difficulty']})")
    
    print("\n🤖 机器人类型:")
    print("   • google_robot_static: Google机器人静态版本（推荐）")
    print("   • google_robot_mobile: Google机器人移动版本")
    print("   • widowx: WidowX机械臂")
    
    print("\n📷 相机类型:")
    print("   • overhead_camera: 顶部俯视相机（推荐）")
    print("   • base_camera: 侧面相机")

def main():
    """主演示函数"""
    print("🎊 SimplerEnv改进接口完整演示")
    print("=" * 60)
    print("本演示将展示所有新功能的使用方法")
    print()
    
    # 显示可用选项
    show_available_options()
    
    # 运行演示
    demos = [
        ("简单接口", demo_simple_interface),
        ("中级接口", demo_custom_interface),
        ("自定义位置", demo_custom_positions),
        ("布局生成器", demo_layout_generators),
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        try:
            success = demo_func()
            results[demo_name] = success
        except Exception as e:
            print(f"❌ {demo_name} 演示异常: {e}")
            results[demo_name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 演示总结:")
    
    total_demos = len(results)
    passed_demos = sum(results.values())
    
    for demo_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {demo_name}: {status}")
    
    print(f"\n总计: {passed_demos}/{total_demos} 演示成功")
    print(f"成功率: {passed_demos/total_demos*100:.1f}%")
    
    print("\n🎉 演示完成！")
    print("\n💡 使用建议:")
    print("   🚀 新手用户: 使用 create_scene_simple() 一键生成")
    print("   🎛️ 有经验用户: 使用 create_scene_custom() 自定义配置")
    print("   🎯 精确控制: 使用 create_scene_with_custom_positions() 指定位置")
    print("   📐 布局生成: 使用 generate_grid_positions() 或 generate_circle_positions()")
    print("\n📖 更多信息请查看函数的docstring文档")

if __name__ == "__main__":
    main()
