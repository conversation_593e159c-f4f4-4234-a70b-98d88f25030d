"""
增强版多样化环境

集成了完整的多样性增强功能，包括光照、材质、物理属性等的随机化。
"""

from typing import Dict, List, Optional, Any
import numpy as np
import sapien.core as sapien

from mani_skill2_real2sim.utils.registration import register_env
from .diverse_scene_env import DiverseSceneEnv

import sys
import os

# 添加多样性增强模块的路径
sys.path.append(os.path.join(os.path.dirname(__file__), "../../../.."))
try:
    from diversity_enhancer import DiversityEnhancer, LightingVariationType, MaterialVariationType
except ImportError:
    # 如果导入失败，创建简单的替代类
    class DiversityEnhancer:
        def __init__(self, seed=None):
            self.rng = np.random.RandomState(seed)
        
        def apply_lighting_variation(self, scene, variation_type=None):
            return {"type": "default"}
        
        def apply_material_variation(self, actor, variation_types=None):
            return {"variations": []}
        
        def apply_physics_variation(self, scene):
            return {"gravity": 9.81}
    
    class LightingVariationType:
        MIXED = "mixed"
    
    class MaterialVariationType:
        FRICTION = "friction"


class EnhancedDiverseSceneEnv(DiverseSceneEnv):
    """增强版多样化场景环境"""

    DEFAULT_ASSET_ROOT = "/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/ManiSkill2_real2sim/data/custom"
    DEFAULT_SCENE_ROOT = "/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/ManiSkill2_real2sim/data/hab2_bench_assets"
    DEFAULT_MODEL_JSON = "info_custom_enhanced_objects_v1.json"

    def __init__(
        self,
        diversity_level: str = "medium",  # low, medium, high, extreme
        lighting_variation_prob: float = 0.8,
        material_variation_prob: float = 0.7,
        physics_variation_prob: float = 0.5,
        camera_variation_prob: float = 0.3,
        diversity_seed: Optional[int] = None,
        material_intensity: str = "medium",  # 新增：材质强度参数
        lighting_intensity: str = "medium",  # 新增：光照强度参数
        custom_params: Optional[Dict] = None,  # 新增：自定义参数
        **kwargs
    ):
        """
        初始化增强版多样化环境

        Args:
            diversity_level: 多样性级别 (low/medium/high/extreme)
            lighting_variation_prob: 光照变化概率
            material_variation_prob: 材质变化概率
            physics_variation_prob: 物理属性变化概率
            camera_variation_prob: 相机变化概率
            diversity_seed: 多样性随机种子
            material_intensity: 材质变化强度 ("subtle", "medium", "dramatic")
            lighting_intensity: 光照变化强度 ("subtle", "medium", "dramatic")
            custom_params: 自定义参数字典
        """
        self.diversity_level = diversity_level
        self.lighting_variation_prob = lighting_variation_prob
        self.material_variation_prob = material_variation_prob
        self.physics_variation_prob = physics_variation_prob
        self.camera_variation_prob = camera_variation_prob

        # 初始化多样性增强器，传递强度参数
        self.diversity_enhancer = DiversityEnhancer(
            seed=diversity_seed,
            material_intensity=material_intensity,
            lighting_intensity=lighting_intensity,
            custom_params=custom_params
        )

        # 根据多样性级别调整参数
        self._adjust_diversity_params()

        # 存储当前episode的多样性信息
        self.current_diversity_info = {}

        super().__init__(**kwargs)
    
    def _adjust_diversity_params(self):
        """根据多样性级别调整参数"""
        if self.diversity_level == "low":
            self.lighting_variation_prob *= 0.5
            self.material_variation_prob *= 0.5
            self.physics_variation_prob *= 0.3
            self.camera_variation_prob *= 0.2
        elif self.diversity_level == "medium":
            # 使用默认值
            pass
        elif self.diversity_level == "high":
            self.lighting_variation_prob = min(1.0, self.lighting_variation_prob * 1.2)
            self.material_variation_prob = min(1.0, self.material_variation_prob * 1.2)
            self.physics_variation_prob = min(1.0, self.physics_variation_prob * 1.5)
            self.camera_variation_prob = min(1.0, self.camera_variation_prob * 1.5)
        elif self.diversity_level == "extreme":
            self.lighting_variation_prob = 1.0
            self.material_variation_prob = 1.0
            self.physics_variation_prob = 0.8
            self.camera_variation_prob = 0.6
    
    def _setup_lighting(self):
        """设置增强的场景光照"""
        if self.bg_name is not None:
            return
        
        # 决定是否应用光照变化
        if self._episode_rng.random() < self.lighting_variation_prob:
            try:
                lighting_info = self.diversity_enhancer.apply_lighting_variation(self._scene)
                self.current_diversity_info["lighting"] = lighting_info
            except Exception as e:
                print(f"光照变化应用失败: {e}")
                super()._setup_lighting()  # 回退到默认光照
        else:
            super()._setup_lighting()  # 使用默认光照
    
    def _load_scene_objects(self):
        """加载场景物体并应用材质变化"""
        super()._load_scene_objects()
        
        # 应用材质变化
        if self._episode_rng.random() < self.material_variation_prob:
            self._apply_material_variations()
        
        # 应用物理属性变化
        if self._episode_rng.random() < self.physics_variation_prob:
            self._apply_physics_variations()
    
    def _apply_material_variations(self):
        """应用材质变化到场景物体"""
        material_info = []
        
        for obj in self.all_scene_objects:
            try:
                # 随机选择要应用的材质变化类型
                material_types = list(MaterialVariationType)
                num_variations = self._episode_rng.randint(1, min(4, len(material_types) + 1))
                # 修复: 使用索引选择来避免numpy choice的维度问题
                selected_indices = self._episode_rng.choice(
                    len(material_types),
                    size=num_variations,
                    replace=False
                )
                variation_types = [material_types[i] for i in selected_indices]
                
                obj_material_info = self.diversity_enhancer.apply_material_variation(
                    obj, variation_types
                )
                obj_material_info["object_name"] = obj.name
                material_info.append(obj_material_info)
                
            except Exception as e:
                print(f"物体 {obj.name} 材质变化应用失败: {e}")
        
        self.current_diversity_info["materials"] = material_info
    
    def _apply_physics_variations(self):
        """应用物理属性变化"""
        try:
            physics_info = self.diversity_enhancer.apply_physics_variation(self._scene)
            self.current_diversity_info["physics"] = physics_info
        except Exception as e:
            print(f"物理属性变化应用失败: {e}")
    
    def _apply_camera_variations(self):
        """应用相机变化（如果需要）"""
        if self._episode_rng.random() < self.camera_variation_prob:
            try:
                # 这里可以添加相机变化逻辑
                # 由于相机配置比较复杂，暂时记录信息
                self.current_diversity_info["camera"] = {"variation_applied": True}
            except Exception as e:
                print(f"相机变化应用失败: {e}")
    
    def reset(self, seed=None, options=None):
        """重置环境并应用多样性增强"""
        # 清空多样性信息
        self.current_diversity_info = {}
        
        # 调用父类reset
        obs, info = super().reset(seed=seed, options=options)
        
        # 应用相机变化
        self._apply_camera_variations()
        
        # 添加多样性信息到info
        info.update({
            "diversity_info": self.current_diversity_info,
            "diversity_level": self.diversity_level
        })
        
        return obs, info
    
    def get_diversity_summary(self) -> Dict[str, Any]:
        """获取当前episode的多样性摘要"""
        summary = {
            "diversity_level": self.diversity_level,
            "applied_variations": list(self.current_diversity_info.keys()),
            "total_objects": len(self.all_scene_objects),
            "target_objects": len(self.target_objects),
            "distractor_objects": len(self.distractor_objects)
        }
        
        # 添加具体的变化信息
        if "lighting" in self.current_diversity_info:
            summary["lighting_type"] = self.current_diversity_info["lighting"].get("type", "unknown")
        
        if "materials" in self.current_diversity_info:
            material_variations = []
            for obj_info in self.current_diversity_info["materials"]:
                for variation in obj_info.get("variations", []):
                    material_variations.append(variation["type"])
            summary["material_variations"] = list(set(material_variations))
        
        if "physics" in self.current_diversity_info:
            summary["physics_gravity"] = self.current_diversity_info["physics"].get("gravity", 9.81)
        
        return summary


# 注册不同多样性级别的环境
@register_env("EnhancedDiverseLowScene-v0", max_episode_steps=80)
class EnhancedDiverseLowSceneEnv(EnhancedDiverseSceneEnv):
    """低多样性增强场景"""
    DEFAULT_MODEL_JSON = "info_custom_diverse_objects_v1.json"
    
    def __init__(self, **kwargs):
        kwargs.setdefault("diversity_level", "low")
        kwargs.setdefault("num_objects_range", (2, 4))
        super().__init__(**kwargs)


@register_env("EnhancedDiverseMediumScene-v0", max_episode_steps=80)
class EnhancedDiverseMediumSceneEnv(EnhancedDiverseSceneEnv):
    """中等多样性增强场景"""
    DEFAULT_MODEL_JSON = "info_custom_diverse_objects_v1.json"
    
    def __init__(self, **kwargs):
        kwargs.setdefault("diversity_level", "medium")
        kwargs.setdefault("num_objects_range", (3, 6))
        super().__init__(**kwargs)


@register_env("EnhancedDiverseHighScene-v0", max_episode_steps=100)
class EnhancedDiverseHighSceneEnv(EnhancedDiverseSceneEnv):
    """高多样性增强场景"""
    DEFAULT_MODEL_JSON = "info_custom_enhanced_objects_v1.json"
    
    def __init__(self, **kwargs):
        kwargs.setdefault("diversity_level", "high")
        kwargs.setdefault("num_objects_range", (4, 8))
        super().__init__(**kwargs)


@register_env("EnhancedDiverseExtremeScene-v0", max_episode_steps=120)
class EnhancedDiverseExtremeSceneEnv(EnhancedDiverseSceneEnv):
    """极高多样性增强场景"""
    DEFAULT_MODEL_JSON = "info_custom_enhanced_objects_v1.json"
    
    def __init__(self, **kwargs):
        kwargs.setdefault("diversity_level", "extreme")
        kwargs.setdefault("num_objects_range", (5, 10))
        kwargs.setdefault("enable_lighting_variation", True)
        kwargs.setdefault("enable_physics_variation", True)
        super().__init__(**kwargs)


# 专门用于训练数据生成的环境
@register_env("TrainingDataGenerationScene-v0", max_episode_steps=80)
class TrainingDataGenerationSceneEnv(EnhancedDiverseSceneEnv):
    """专门用于生成训练数据的多样化场景"""
    DEFAULT_MODEL_JSON = "info_custom_enhanced_objects_v1.json"
    
    def __init__(self, **kwargs):
        kwargs.setdefault("diversity_level", "high")
        kwargs.setdefault("num_objects_range", (3, 7))
        kwargs.setdefault("lighting_variation_prob", 0.9)
        kwargs.setdefault("material_variation_prob", 0.8)
        kwargs.setdefault("physics_variation_prob", 0.6)
        kwargs.setdefault("enable_random_config", True)
        super().__init__(**kwargs)
    
    def generate_training_batch(self, batch_size: int = 100, base_seed: int = 42) -> List[Dict[str, Any]]:
        """生成一批训练数据配置"""
        batch_data = []
        
        for i in range(batch_size):
            episode_seed = base_seed + i
            obs, info = self.reset(seed=episode_seed)
            
            batch_data.append({
                "episode_id": i,
                "seed": episode_seed,
                "diversity_summary": self.get_diversity_summary(),
                "scene_config": info.get("scene_config", {}),
                "language_instruction": self.get_language_instruction()
            })
        
        return batch_data
