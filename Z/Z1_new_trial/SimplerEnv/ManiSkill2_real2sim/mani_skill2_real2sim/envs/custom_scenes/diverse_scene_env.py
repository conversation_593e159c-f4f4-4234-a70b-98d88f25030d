"""
多样化自定义场景环境

这个模块实现了支持动态配置场景参数的自定义环境类，
用于增强训练数据的多样性。
"""

from collections import OrderedDict
from typing import List, Optional, Dict, Any, Tuple
import numpy as np
import sapien.core as sapien
from transforms3d.euler import euler2quat
from transforms3d.quaternions import axangle2quat, qmult

from mani_skill2_real2sim import ASSET_DIR
from mani_skill2_real2sim.utils.common import random_choice
from mani_skill2_real2sim.utils.registration import register_env
from mani_skill2_real2sim.utils.sapien_utils import vectorize_pose

from .base_env import CustomSceneEnv, CustomOtherObjectsInSceneEnv
import sys
import os

# 添加自定义配置模块的路径
sys.path.append(os.path.join(os.path.dirname(__file__), "../../../.."))
try:
    from custom_scene_config import CustomSceneConfig, SceneConfigGenerator, ObjectConfig, LightingMode
except ImportError:
    # 如果导入失败，定义基本的配置类
    from dataclasses import dataclass, field
    from enum import Enum
    
    class LightingMode(Enum):
        DEFAULT = "default"
        BRIGHT = "bright"
        DIM = "dim"
    
    @dataclass
    class ObjectConfig:
        object_type: str
        model_id: str
        scale: float = 1.0
        density: float = 1000.0
    
    @dataclass
    class CustomSceneConfig:
        scene_name: str = "custom_scene"
        target_objects: List[ObjectConfig] = field(default_factory=list)
        distractor_objects: List[ObjectConfig] = field(default_factory=list)


class DiverseSceneEnv(CustomSceneEnv):
    """支持多样化配置的自定义场景环境"""

    DEFAULT_ASSET_ROOT = "/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/ManiSkill2_real2sim/data/custom"
    DEFAULT_SCENE_ROOT = "/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/ManiSkill2_real2sim/data/hab2_bench_assets"
    DEFAULT_MODEL_JSON = "info_custom_diverse_objects_v1.json"

    def __init__(
        self,
        scene_config: Optional[CustomSceneConfig] = None,
        enable_random_config: bool = True,
        config_seed: Optional[int] = None,
        num_objects_range: Tuple[int, int] = (3, 8),
        enable_lighting_variation: bool = True,
        enable_physics_variation: bool = True,
        prepackaged_config: bool = False,
        **kwargs,
    ):
        """
        初始化多样化场景环境
        
        Args:
            scene_config: 预定义的场景配置
            enable_random_config: 是否启用随机配置生成
            config_seed: 配置生成的随机种子
            num_objects_range: 物体数量范围
            enable_lighting_variation: 是否启用光照变化
            enable_physics_variation: 是否启用物理属性变化
            prepackaged_config: 是否使用预打包配置
        """
        self.scene_config = scene_config
        self.enable_random_config = enable_random_config
        self.config_seed = config_seed
        self.num_objects_range = num_objects_range
        self.enable_lighting_variation = enable_lighting_variation
        self.enable_physics_variation = enable_physics_variation
        
        # 初始化配置生成器
        self.config_generator = None
        if self.enable_random_config:
            try:
                self.config_generator = SceneConfigGenerator()
            except:
                self.config_generator = None
        
        # 场景物体
        self.target_objects = []
        self.distractor_objects = []
        self.all_scene_objects = []
        
        # 当前配置
        self.current_config = None
        
        # 物理属性变化
        self.physics_variations = {
            "friction_multiplier": (0.5, 2.0),
            "restitution_multiplier": (0.1, 2.0),
            "density_multiplier": (0.5, 2.0)
        }
        
        self.prepackaged_config = prepackaged_config
        if self.prepackaged_config:
            kwargs.update(self._setup_prepackaged_env_init_config())
        
        super().__init__(**kwargs)
    
    def _setup_prepackaged_env_init_config(self):
        """设置预打包环境配置"""
        ret = {}
        ret["robot"] = "google_robot_static"
        ret["control_freq"] = 3
        ret["sim_freq"] = 513
        ret["control_mode"] = (
            "arm_pd_ee_delta_pose_align_interpolate_by_planner_gripper_pd_joint_target_delta_pos_interpolate_by_planner"
        )
        ret["scene_name"] = "google_pick_coke_can_1_v4"
        ret["camera_cfgs"] = {"add_segmentation": True}
        ret["rgb_overlay_path"] = str(
            ASSET_DIR / "real_inpainting/google_coke_can_real_eval_1.png"
        )
        ret["rgb_overlay_cameras"] = ["overhead_camera"]
        return ret
    
    def _generate_scene_config(self, seed: Optional[int] = None) -> CustomSceneConfig:
        """生成场景配置"""
        if self.scene_config is not None:
            return self.scene_config
        
        if self.config_generator is not None and self.enable_random_config:
            return self.config_generator.generate_random_config(
                num_objects_range=self.num_objects_range,
                include_distractors=True,
                seed=seed
            )
        
        # 回退到简单配置
        return CustomSceneConfig(
            scene_name="simple_diverse_scene",
            target_objects=[ObjectConfig("cube", "green_cube_3cm")],
            distractor_objects=[ObjectConfig("can", "coke_can")]
        )
    
    def _load_actors(self):
        """加载场景中的所有物体"""
        self._load_arena_helper()
        self._load_scene_objects()
        
        # 设置物体阻尼
        for obj in self.all_scene_objects:
            obj.set_damping(0.1, 0.1)
    
    def _load_scene_objects(self):
        """根据当前配置加载场景物体"""
        if self.current_config is None:
            print("警告：没有场景配置，无法加载物体")
            return

        # 清除现有物体
        for obj in self.all_scene_objects:
            if obj is not None and obj in self._scene.get_all_actors():
                self._scene.remove_actor(obj)

        self.target_objects = []
        self.distractor_objects = []
        self.all_scene_objects = []

        print(f"开始加载场景物体...")
        print(f"目标物体配置: {len(self.current_config.target_objects)} 个")
        print(f"干扰物体配置: {len(self.current_config.distractor_objects)} 个")

        # 加载目标物体
        target_created = 0
        for i, obj_config in enumerate(self.current_config.target_objects):
            print(f"创建目标物体 {i+1}/{len(self.current_config.target_objects)}: {obj_config.model_id}")
            obj = self._create_object_from_config(obj_config, is_target=True)
            if obj is not None:
                self.target_objects.append(obj)
                self.all_scene_objects.append(obj)
                target_created += 1
            else:
                print(f"目标物体创建失败: {obj_config.model_id}")

        # 加载干扰物体
        distractor_created = 0
        for i, obj_config in enumerate(self.current_config.distractor_objects):
            print(f"创建干扰物体 {i+1}/{len(self.current_config.distractor_objects)}: {obj_config.model_id}")
            obj = self._create_object_from_config(obj_config, is_target=False)
            if obj is not None:
                self.distractor_objects.append(obj)
                self.all_scene_objects.append(obj)
                distractor_created += 1
            else:
                print(f"干扰物体创建失败: {obj_config.model_id}")

        print(f"物体加载完成: 目标物体 {target_created}/{len(self.current_config.target_objects)}, "
              f"干扰物体 {distractor_created}/{len(self.current_config.distractor_objects)}")
        print(f"总共成功创建 {len(self.all_scene_objects)} 个物体")
    
    def _create_object_from_config(self, obj_config: ObjectConfig, is_target: bool = True) -> Optional[sapien.Actor]:
        """根据配置创建物体"""
        try:
            # 验证模型ID是否存在于数据库中
            if obj_config.model_id not in self.model_db:
                print(f"错误：模型ID '{obj_config.model_id}' 不存在于模型数据库中")
                print(f"可用的模型ID: {list(self.model_db.keys())}")
                return None

            # 验证模型文件是否存在
            model_dir = self.asset_root / "models" / obj_config.model_id
            collision_file = model_dir / "collision.obj"
            if not collision_file.exists():
                print(f"错误：碰撞文件不存在: {collision_file}")
                return None

            # 检查视觉文件
            visual_files = [
                model_dir / "textured.obj",
                model_dir / "textured.dae",
                model_dir / "textured.glb"
            ]
            visual_file_exists = any(f.exists() for f in visual_files)
            if not visual_file_exists:
                print(f"错误：没有找到视觉文件在: {model_dir}")
                return None

            # 获取物体密度（从数据库或配置）
            model_info = self.model_db[obj_config.model_id]
            density = model_info.get('density', getattr(obj_config, 'density', 1000.0))

            # 应用物理属性变化
            if self.enable_physics_variation:
                density *= np.random.uniform(*self.physics_variations["density_multiplier"])

            # 创建物理材质
            friction_mult = 1.0
            restitution_mult = 1.0
            if self.enable_physics_variation:
                friction_mult = np.random.uniform(*self.physics_variations["friction_multiplier"])
                restitution_mult = np.random.uniform(*self.physics_variations["restitution_multiplier"])

            material = self._scene.create_physical_material(
                static_friction=0.8 * friction_mult,
                dynamic_friction=0.6 * friction_mult,
                restitution=0.1 * restitution_mult,
            )

            # 创建物体
            print(f"正在创建物体: {obj_config.model_id}")
            obj = CustomOtherObjectsInSceneEnv._build_actor_helper(
                model_id=obj_config.model_id,
                scene=self._scene,
                scale=getattr(obj_config, 'scale', 1.0),
                density=density,
                physical_material=material,
                root_dir=self.asset_root,
            )
            obj.name = f"{obj_config.model_id}_{'target' if is_target else 'distractor'}"

            print(f"成功创建物体: {obj.name}")
            return obj

        except Exception as e:
            print(f"创建物体失败 {obj_config.model_id}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _setup_lighting(self):
        """设置场景光照"""
        if self.bg_name is not None:
            return
        
        # 获取光照配置
        lighting_mode = LightingMode.DEFAULT
        if (self.current_config is not None and 
            hasattr(self.current_config, 'lighting') and 
            hasattr(self.current_config.lighting, 'mode')):
            lighting_mode = self.current_config.lighting.mode
        elif self.enable_lighting_variation:
            lighting_mode = np.random.choice(list(LightingMode))
        
        shadow = self.enable_shadow
        
        # 根据模式设置光照
        if lighting_mode == LightingMode.BRIGHT:
            self._scene.set_ambient_light([0.5, 0.5, 0.5])
            self._scene.add_directional_light(
                [0, 0, -1], [3.0, 3.0, 3.0], shadow=shadow, scale=5, shadow_map_size=2048
            )
            self._scene.add_directional_light([-1, -0.5, -1], [1.0, 1.0, 1.0])
            self._scene.add_directional_light([1, 1, -1], [1.0, 1.0, 1.0])
        elif lighting_mode == LightingMode.DIM:
            self._scene.set_ambient_light([0.1, 0.1, 0.1])
            self._scene.add_directional_light(
                [1, 1, -1], [0.5, 0.5, 0.5], shadow=shadow, scale=5, shadow_map_size=2048
            )
            self._scene.add_directional_light([0, 0, -1], [0.5, 0.5, 0.5])
        else:  # DEFAULT
            self._scene.set_ambient_light([0.3, 0.3, 0.3])
            self._scene.add_directional_light(
                [0, 0, -1], [2.2, 2.2, 2.2], shadow=shadow, scale=5, shadow_map_size=2048
            )
            self._scene.add_directional_light([-1, -0.5, -1], [0.7, 0.7, 0.7])
            self._scene.add_directional_light([1, 1, -1], [0.7, 0.7, 0.7])
    
    def _initialize_actors(self):
        """稳定的物体初始化方法 - 确保物体不掉落"""
        if not self.all_scene_objects:
            print("警告：没有物体需要初体化")
            return

        print(f"开始稳定初始化 {len(self.all_scene_objects)} 个物体")

        # 预定义安全位置 - 手动指定已知安全的位置
        safe_positions = [
            (-0.30, 0.10),  # 位置1
            (-0.25, 0.10),  # 位置2
            (-0.20, 0.10),  # 位置3
            (-0.30, 0.20),  # 位置4
            (-0.25, 0.20),  # 位置5
            (-0.20, 0.20),  # 位置6
            (-0.30, 0.30),  # 位置7
            (-0.25, 0.30),  # 位置8
            (-0.20, 0.30),  # 位置9
            (-0.35, 0.15),  # 位置10
            (-0.15, 0.15),  # 位置11
            (-0.35, 0.25),  # 位置12
        ]

        print(f"使用预定义的 {len(safe_positions)} 个安全位置")

        # 将机器人移到远处避免碰撞
        self.agent.robot.set_pose(sapien.Pose([-10, 0, 0]))

        # 逐个放置物体，确保每个都稳定
        for i, obj in enumerate(self.all_scene_objects):
            if obj is None:
                print(f"警告：第{i}个物体为None，跳过")
                continue

            # 使用预定义的安全位置
            if i < len(safe_positions):
                x, y = safe_positions[i]
            else:
                # 如果物体太多，使用随机位置但更保守
                x = self._episode_rng.uniform(-0.30, -0.20)
                y = self._episode_rng.uniform(0.10, 0.30)

            # 直接放在桌面上，不要从高处落下
            z = self.scene_table_height + 0.02  # 只比桌面高一点点

            # 不使用随机旋转，保持物体稳定
            q = euler2quat(0, 0, 0)  # 无旋转

            # 设置物体位置
            print(f"稳定放置物体 {obj.name} 在位置: ({x:.2f}, {y:.2f}, {z:.2f})")
            obj.set_pose(sapien.Pose([x, y, z], q))

            # 设置阻尼减少运动
            obj.set_damping(0.5, 0.5)

            # 每放置一个物体就稳定一下
            self._settle(0.3)

            # 检查物体是否稳定
            pos_after = obj.pose.p
            if pos_after[2] < self.scene_table_height - 0.05:
                print(f"  ⚠ 警告: {obj.name} 位置不稳定，重新调整")
                # 重新放置到更安全的位置
                obj.set_pose(sapien.Pose([x, y, self.scene_table_height + 0.05], q))
                obj.set_velocity(np.zeros(3))
                obj.set_angular_velocity(np.zeros(3))
                self._settle(0.5)

        # 最终全体稳定
        print("最终稳定所有物体...")
        self._settle(1.0)

        # 检查并修正任何不稳定的物体
        print("检查并修正物体位置...")
        for i, obj in enumerate(self.all_scene_objects):
            if obj is not None:
                pos = obj.pose.p
                if pos[2] < self.scene_table_height - 0.05:
                    print(f"  修正掉落的物体: {obj.name}")
                    # 强制放回桌面
                    if i < len(safe_positions):
                        x, y = safe_positions[i]
                    else:
                        x, y = -0.25, 0.20
                    obj.set_pose(sapien.Pose([x, y, self.scene_table_height + 0.05], euler2quat(0, 0, 0)))
                    obj.set_velocity(np.zeros(3))
                    obj.set_angular_velocity(np.zeros(3))

        # 最终稳定
        self._settle(0.5)

        print("稳定物体初始化完成")

    def evaluate(self, **kwargs):
        """评估环境状态"""
        # 基本的评估信息
        eval_info = {
            "success": False,  # 默认未成功
            "num_objects": len(self.all_scene_objects),
            "scene_config": self.current_config.scene_name if self.current_config else "unknown"
        }

        # 如果有目标物体，检查是否达成目标
        if hasattr(self, 'target_objects') and self.target_objects:
            # 这里可以添加具体的成功条件检查
            # 例如：物体是否被抓取、是否到达目标位置等
            pass

        return eval_info

    def reset(self, seed=None, options=None):
        """重置环境"""
        if options is None:
            options = dict()
        
        # 生成新的场景配置
        config_seed = seed if seed is not None else self.config_seed
        if config_seed is not None:
            config_seed = (config_seed + hash(str(options))) % (2**32)
        
        self.current_config = self._generate_scene_config(config_seed)
        
        # 设置episode随机数生成器
        self.set_episode_rng(seed)
        
        # 检查是否需要重新配置
        reconfigure = options.get("reconfigure", True)  # 默认重新配置以加载新物体
        options["reconfigure"] = reconfigure
        
        # 调用父类reset
        obs, info = super().reset(seed=self._episode_seed, options=options)
        
        # 添加配置信息到info
        info.update({
            "scene_config": {
                "scene_name": self.current_config.scene_name,
                "num_target_objects": len(self.target_objects),
                "num_distractor_objects": len(self.distractor_objects),
                "total_objects": len(self.all_scene_objects)
            }
        })
        
        return obs, info
    
    def get_language_instruction(self):
        """获取语言指令"""
        if not self.target_objects:
            return "Pick up an object"
        
        # 根据目标物体生成指令
        target_obj = self.target_objects[0]
        obj_name = target_obj.name.replace("_target", "").replace("_", " ")
        
        instructions = [
            f"Pick up the {obj_name}",
            f"Grasp the {obj_name}",
            f"Get the {obj_name}",
            f"Take the {obj_name}"
        ]
        
        return self._episode_rng.choice(instructions)
    
    def evaluate_success(self) -> bool:
        """评估任务是否成功"""
        if not self.target_objects:
            return False
        
        # 检查目标物体是否被抓取并提升
        target_obj = self.target_objects[0]
        current_height = target_obj.pose.p[2]
        
        # 如果物体高度超过桌面一定距离，认为成功
        success_height_threshold = self.scene_table_height + 0.1
        return current_height > success_height_threshold


# 注册不同变体的环境
@register_env("DiversePickScene-v0", max_episode_steps=80)
class DiversePickSceneEnv(DiverseSceneEnv):
    """多样化拾取场景环境"""
    DEFAULT_MODEL_JSON = "info_custom_diverse_objects_v1.json"

    def __init__(self, **kwargs):
        kwargs.setdefault("num_objects_range", (2, 5))
        kwargs.setdefault("enable_lighting_variation", True)
        kwargs.setdefault("enable_physics_variation", True)
        super().__init__(**kwargs)


@register_env("DiverseEnhancedScene-v0", max_episode_steps=80)
class DiverseEnhancedSceneEnv(DiverseSceneEnv):
    """增强版多样化场景环境"""
    DEFAULT_MODEL_JSON = "info_custom_enhanced_objects_v1.json"

    def __init__(self, **kwargs):
        kwargs.setdefault("num_objects_range", (3, 8))
        kwargs.setdefault("enable_lighting_variation", True)
        kwargs.setdefault("enable_physics_variation", True)
        super().__init__(**kwargs)


@register_env("DiverseClutteredScene-v0", max_episode_steps=120)
class DiverseClutteredSceneEnv(DiverseSceneEnv):
    """杂乱多样化场景环境"""
    DEFAULT_MODEL_JSON = "info_custom_enhanced_objects_v1.json"

    def __init__(self, **kwargs):
        kwargs.setdefault("num_objects_range", (5, 12))
        kwargs.setdefault("enable_lighting_variation", True)
        kwargs.setdefault("enable_physics_variation", True)
        super().__init__(**kwargs)

    def _initialize_actors(self):
        """在杂乱场景中初始化物体位置"""
        if not self.all_scene_objects:
            return

        # 更紧密的工作空间
        workspace_bounds = ((-0.3, 0.3), (-0.3, 0.3))
        min_spacing = 0.03  # 更小的间距创造杂乱感

        placed_positions = []

        for obj in self.all_scene_objects:
            for attempt in range(100):  # 更多尝试次数
                x = self._episode_rng.uniform(workspace_bounds[0][0], workspace_bounds[0][1])
                y = self._episode_rng.uniform(workspace_bounds[1][0], workspace_bounds[1][1])
                z = self.scene_table_height + self._episode_rng.uniform(0.3, 0.8)  # 不同高度

                position = np.array([x, y])
                too_close = False
                for placed_pos in placed_positions:
                    if np.linalg.norm(position - placed_pos) < min_spacing:
                        too_close = True
                        break

                if not too_close:
                    placed_positions.append(position)
                    break
            else:
                # 强制放置
                x = self._episode_rng.uniform(workspace_bounds[0][0], workspace_bounds[0][1])
                y = self._episode_rng.uniform(workspace_bounds[1][0], workspace_bounds[1][1])
                z = self.scene_table_height + 0.5

            # 更大的随机旋转范围
            rot_x = self._episode_rng.uniform(-0.2, 0.2)
            rot_y = self._episode_rng.uniform(-0.2, 0.2)
            rot_z = self._episode_rng.uniform(0, 2 * np.pi)
            q = euler2quat(rot_x, rot_y, rot_z)

            obj.set_pose(sapien.Pose([x, y, z], q))
            # 锁定x和y轴旋转，让物体自然落到桌面
            obj.lock_motion(0, 0, 0, 1, 1, 0)

        self.agent.robot.set_pose(sapien.Pose([-10, 0, 0]))

        # 让物体落到桌面
        self._settle(0.5)

        # 解锁物体运动
        for obj in self.all_scene_objects:
            obj.lock_motion(0, 0, 0, 0, 0, 0)
            # 确保物体不会休眠
            obj.set_pose(obj.pose)
            obj.set_velocity(np.zeros(3))
            obj.set_angular_velocity(np.zeros(3))

        # 再次稳定
        self._settle(0.5)

        # 检查是否需要更长时间稳定
        total_lin_vel, total_ang_vel = 0.0, 0.0
        for obj in self.all_scene_objects:
            total_lin_vel += np.linalg.norm(obj.velocity)
            total_ang_vel += np.linalg.norm(obj.angular_velocity)

        if total_lin_vel > 1e-3 or total_ang_vel > 1e-2:
            self._settle(1.5)


@register_env("SimpleDiverseTestEnv-v1", max_episode_steps=100)
class SimpleDiverseTestEnv(DiverseSceneEnv):
    """简化的测试环境 - 用于验证修复效果"""

    def __init__(self, **kwargs):
        # 使用固定的简单配置进行测试
        kwargs.setdefault("enable_random_config", False)
        kwargs.setdefault("enable_lighting_variation", False)
        kwargs.setdefault("enable_physics_variation", False)
        super().__init__(**kwargs)

    def reset(self, seed=None, options=None):
        """重置环境，使用预定义的简单配置"""
        # 创建简单的测试配置
        test_config = CustomSceneConfig(
            scene_name="simple_test_scene",
            target_objects=[
                ObjectConfig("can", "coke_can"),
                ObjectConfig("cube", "green_cube_3cm")
            ],
            distractor_objects=[
                ObjectConfig("fruit", "apple")
            ]
        )

        self.current_config = test_config
        print(f"使用测试配置: {len(test_config.target_objects)} 个目标物体, "
              f"{len(test_config.distractor_objects)} 个干扰物体")

        return super().reset(seed=seed, options=options)
