<?xml version="1.0" encoding="UTF-8"?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from C:\Users\<USER>\Anaconda3\envs\urdf_analyzer\Lib\site-packages\rtbdata\xacro\interbotix_descriptions\urdf\wx250s.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="wx250s">
  <link name="base_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-1-Base.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-1-Base.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.0380446000 0.0006138920 0.0193354000"/>
      <mass value="0.538736"/>
      <inertia ixx="0.0021150000" ixy="-0.0000163500" ixz="0.0000006998" iyy="0.0006921000" iyz="0.0000464200" izz="0.0025240000"/>
    </inertial>
  </link>
  <joint name="waist" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="10" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0 0 0.072"/>
    <parent link="base_link"/>
    <child link="shoulder_link"/>
  </joint>
  <!-- <transmission name="trans_waist">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="waist">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="waist_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission> -->
  <link name="shoulder_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 -0.003"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-2-Shoulder.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 -0.003"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-2-Shoulder.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0000223482 0.0000414609 0.0066287000"/>
      <mass value="0.480879"/>
      <inertia ixx="0.0003790000" ixy="0.0000000022" ixz="-0.0000003561" iyy="0.0005557000" iyz="0.0000012245" izz="0.0005889000"/>
    </inertial>
  </link>
  <joint name="shoulder" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="20" lower="-1.8849555921538759" upper="1.9896753472735358" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0 0 0.03865"/>
    <parent link="shoulder_link"/>
    <child link="upper_arm_link"/>
  </joint>
  <!-- <transmission name="trans_shoulder">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="shoulder">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="shoulder_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission> -->
  <link name="upper_arm_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-3-UA.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-3-UA.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0171605000 0.0000002725 0.1913230000"/>
      <mass value="0.430811"/>
      <inertia ixx="0.0034630000" ixy="-0.0000000001" ixz="-0.0000000002" iyy="0.0035870000" iyz="0.0004272000" izz="0.0004566000"/>
    </inertial>
  </link>
  <joint name="elbow" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="15" lower="-2.1467549799530254" upper="1.6057029118347832" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0.04975 0 0.25"/>
    <parent link="upper_arm_link"/>
    <child link="upper_forearm_link"/>
  </joint>
  <!-- <transmission name="trans_elbow">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="elbow">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="elbow_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission> -->
  <link name="upper_forearm_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-4-UF.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-4-UF.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.1079630000 0.0001158760 0"/>
      <mass value="0.234589"/>
      <inertia ixx="0.0000397100" ixy="0.0000023528" ixz="0" iyy="0.0008878000" iyz="0" izz="0.0008880000"/>
    </inertial>
  </link>
  <joint name="forearm_roll" type="revolute">
    <axis xyz="1 0 0"/>
    <limit effort="2" lower="-3.141582653589793" upper="3.141582653589793" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0.175 0 0"/>
    <parent link="upper_forearm_link"/>
    <child link="lower_forearm_link"/>
  </joint>
  <!-- <transmission name="trans_forearm_roll">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="forearm_roll">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="forearm_roll_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission> -->
  <link name="lower_forearm_link">
    <visual>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-5-LF.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-5-LF.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="3.141592653589793 0 0" xyz="0.0374395000 0.0052225200 0"/>
      <mass value="0.220991"/>
      <inertia ixx="0.0000636900" ixy="-0.0000229200" ixz="0" iyy="0.0001677000" iyz="0" izz="0.0001834000"/>
    </inertial>
  </link>
  <joint name="wrist_angle" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="5" lower="-1.7453292519943295" upper="2.1467549799530254" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0.075 0 0"/>
    <parent link="lower_forearm_link"/>
    <child link="wrist_link"/>
  </joint>
  <!-- <transmission name="trans_wrist_angle">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="wrist_angle">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="wrist_angle_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission> -->
  <link name="wrist_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-6-Wrist.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-6-Wrist.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0423600000 -0.0000106630 0.0105770000"/>
      <mass value="0.084957"/>
      <inertia ixx="0.0000308200" ixy="0.0000000191" ixz="0.0000000023" iyy="0.0000282200" iyz="0.0000025481" izz="0.0000315200"/>
    </inertial>
  </link>
  <joint name="wrist_rotate" type="revolute">
    <axis xyz="1 0 0"/>
    <limit effort="1" lower="-3.141582653589793" upper="3.141582653589793" velocity="3.141592653589793"/>
    <origin rpy="0 0 0" xyz="0.065 0 0"/>
    <parent link="wrist_link"/>
    <child link="gripper_link"/>
  </joint>
  <!-- <transmission name="trans_wrist_rotate">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="wrist_rotate">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="wrist_rotate_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission> -->
  <link name="gripper_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.02 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-7-Gripper.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.02 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-7-Gripper.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0216310000 0.0000002516 0.0114100000"/>
      <mass value="0.072885"/>
      <inertia ixx="0.0000253700" ixy="0.0000000000" ixz="0.0000000000" iyy="0.0000183600" iyz="0.0000004340" izz="0.0000167400"/>
    </inertial>
  </link>
  <joint name="ee_arm" type="fixed">
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0.043 0 0"/>
    <parent link="gripper_link"/>
    <child link="ee_arm_link"/>
  </joint>
  <link name="ee_arm_link">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>
  <joint name="gripper" type="fixed">
    <axis xyz="1 0 0"/>
    <!-- <limit effort="1" velocity="3.141592653589793"/> -->
    <origin rpy="0 0 0" xyz="0.0055 0 0"/>
    <parent link="ee_arm_link"/>
    <child link="gripper_prop_link"/>
  </joint>
  <!-- <transmission name="trans_gripper">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="gripper">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="gripper_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission> -->
  <link name="gripper_prop_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.0685 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-8-Gripper-Prop.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.0685 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-8-Gripper-Prop.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0008460000 -0.0000016817 0.0000420000"/>
      <mass value="0.00434"/>
      <inertia ixx="0.0000005923" ixy="0.0000000000" ixz="0.0000003195" iyy="0.0000011156" iyz="-0.0000000004" izz="0.0000005743"/>
    </inertial>
  </link>
  <!-- If the default gripper bar is being used, then also add the gripper bar -->
  <joint name="gripper_bar" type="fixed">
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="ee_arm_link"/>
    <child link="gripper_bar_link"/>
  </joint>
  <link name="gripper_bar_link">
    <visual>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.063 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-9-Gripper-Bar.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 1.5707963267948966" xyz="-0.063 0 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-9-Gripper-Bar.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 1.5707963267948966" xyz="0.0096870000 0.0000008177 0.0049620000"/>
      <mass value="0.034199"/>
      <inertia ixx="0.0000074125" ixy="-0.0000000008" ixz="-0.0000000006" iyy="0.0000284300" iyz="-0.0000013889" izz="0.0000286000"/>
    </inertial>
  </link>
  <joint name="ee_bar" type="fixed">
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0.023 0 0"/>
    <parent link="gripper_bar_link"/>
    <child link="fingers_link"/>
  </joint>
  <link name="fingers_link">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>
  <!-- If the default gripper fingers are being used, add those as well -->
  <joint name="left_finger" type="prismatic">
    <axis xyz="0 1 0"/>
    <limit effort="5" lower="0.015" upper="0.037" velocity="1"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="fingers_link"/>
    <child link="left_finger_link"/>
  </joint>
  <!-- <transmission name="trans_left_finger">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="left_finger">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="left_finger_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission> -->
  <link name="left_finger_link">
    <visual>
      <origin rpy="3.141592653589793 3.141592653589793 0" xyz="0 0.005 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-10-Finger.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="3.141592653589793 3.141592653589793 0" xyz="0 0.005 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-10-Finger.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="3.141592653589793 3.141592653589793 1.5707963267948966" xyz="0.0138160000 0.0000000000 0.0000000000"/>
      <mass value="0.016246"/>
      <inertia ixx="0.0000047310" ixy="-0.0000004560" ixz="0.0000000000" iyy="0.0000015506" iyz="0.0000000000" izz="0.0000037467"/>
    </inertial>
  </link>
  <joint name="right_finger" type="prismatic">
    <axis xyz="0 -1 0"/>
    <limit effort="5" lower="0.015" upper="0.037" velocity="1"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="fingers_link"/>
    <child link="right_finger_link"/>
    <mimic joint="left_finger" multiplier="1" offset="0"/>
  </joint>
  <!-- <transmission name="trans_right_finger">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="right_finger">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="right_finger_motor">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission> -->
  <link name="right_finger_link">
    <visual>
      <origin rpy="0 3.141592653589793 0" xyz="0 -0.005 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-10-Finger.obj" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 3.141592653589793 0" xyz="0 -0.005 0"/>
      <geometry>
        <mesh filename="meshes_wx250s/WXSA-250-M-10-Finger.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 3.141592653589793 1.5707963267948966" xyz="0.0138160000 0.0000000000  0.0000000000"/>
      <mass value="0.016246"/>
      <inertia ixx="0.0000047310" ixy="0.0000004560" ixz="0.0000000000" iyy="0.0000015506" iyz="0.0000000000" izz="0.0000037467"/>
    </inertial>
  </link>
  <joint name="ee_gripper" type="fixed">
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0.027575 0 0"/>
    <parent link="fingers_link"/>
    <child link="ee_gripper_link"/>
  </joint>
  <link name="ee_gripper_link">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>
</robot>
