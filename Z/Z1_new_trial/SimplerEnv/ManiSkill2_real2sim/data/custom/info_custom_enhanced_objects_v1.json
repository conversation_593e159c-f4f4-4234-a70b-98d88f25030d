{"comment": "增强版自定义物体数据库 - 包含材质、纹理和物理属性变化", "version": "1.0", "created_for": "Z1_new_trial_enhanced_diversity", "_基础几何体_多材质变体": {"cube_wood_light": {"bbox": {"min": [-0.02, -0.02, -0.02], "max": [0.02, 0.02, 0.02]}, "scales": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0], "density": 600, "color_variant": "brown", "object_type": "cube", "material_type": "wood", "material_properties": {"static_friction": 0.7, "dynamic_friction": 0.5, "restitution": 0.2}, "texture_variants": ["wood_oak", "wood_pine", "wood_mahogany"]}, "cube_metal_heavy": {"bbox": {"min": [-0.02, -0.02, -0.02], "max": [0.02, 0.02, 0.02]}, "scales": [0.5, 0.75, 1.0, 1.25, 1.5], "density": 2700, "color_variant": "gray", "object_type": "cube", "material_type": "metal", "material_properties": {"static_friction": 0.9, "dynamic_friction": 0.7, "restitution": 0.05}, "texture_variants": ["metal_brushed", "metal_polished", "metal_rusty"]}, "cube_plastic_light": {"bbox": {"min": [-0.02, -0.02, -0.02], "max": [0.02, 0.02, 0.02]}, "scales": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0], "density": 900, "color_variant": "random", "object_type": "cube", "material_type": "plastic", "material_properties": {"static_friction": 0.6, "dynamic_friction": 0.4, "restitution": 0.3}, "color_variants": ["red", "blue", "green", "yellow", "purple", "orange", "white", "black"], "texture_variants": ["plastic_smooth", "plastic_textured", "plastic_matte"]}}, "_球体_多种材质": {"sphere_rubber_bouncy": {"bbox": {"min": [-0.025, -0.025, -0.025], "max": [0.025, 0.025, 0.025]}, "scales": [0.6, 0.8, 1.0, 1.2, 1.4], "density": 400, "color_variant": "random", "object_type": "sphere", "material_type": "rubber", "material_properties": {"static_friction": 0.8, "dynamic_friction": 0.6, "restitution": 0.8}, "color_variants": ["red", "blue", "green", "yellow", "orange", "purple"], "texture_variants": ["rubber_smooth", "rubber_textured"]}, "sphere_glass_fragile": {"bbox": {"min": [-0.02, -0.02, -0.02], "max": [0.02, 0.02, 0.02]}, "scales": [0.7, 1.0, 1.3], "density": 2500, "color_variant": "transparent", "object_type": "sphere", "material_type": "glass", "material_properties": {"static_friction": 0.3, "dynamic_friction": 0.2, "restitution": 0.1}, "color_variants": ["clear", "blue_tint", "green_tint", "amber"], "texture_variants": ["glass_clear", "glass_frosted"]}}, "_圆柱体_多种用途": {"cylinder_can_aluminum": {"bbox": {"min": [-0.03, -0.03, -0.05], "max": [0.03, 0.03, 0.05]}, "scales": [0.7, 0.85, 1.0, 1.15, 1.3], "density": 2700, "color_variant": "silver", "object_type": "cylinder", "material_type": "aluminum", "material_properties": {"static_friction": 0.8, "dynamic_friction": 0.6, "restitution": 0.1}, "texture_variants": ["aluminum_brushed", "aluminum_polished", "aluminum_anodized"]}, "cylinder_bottle_plastic": {"bbox": {"min": [-0.025, -0.025, -0.06], "max": [0.025, 0.025, 0.06]}, "scales": [0.8, 1.0, 1.2, 1.4], "density": 950, "color_variant": "random", "object_type": "cylinder", "material_type": "plastic", "material_properties": {"static_friction": 0.6, "dynamic_friction": 0.4, "restitution": 0.2}, "color_variants": ["blue", "green", "red", "white", "black"], "texture_variants": ["plastic_smooth", "plastic_ribbed"]}}, "_复杂形状_日常物品": {"mug_ceramic_handle": {"bbox": {"min": [-0.04, -0.03, -0.04], "max": [0.04, 0.03, 0.04]}, "scales": [0.8, 1.0, 1.2], "density": 2000, "color_variant": "random", "object_type": "container", "material_type": "ceramic", "material_properties": {"static_friction": 0.7, "dynamic_friction": 0.5, "restitution": 0.05}, "color_variants": ["white", "blue", "red", "green", "yellow", "black"], "texture_variants": ["ceramic_glossy", "ceramic_matte", "ceramic_patterned"]}, "book_paper_thick": {"bbox": {"min": [-0.06, -0.04, -0.01], "max": [0.06, 0.04, 0.01]}, "scales": [0.8, 1.0, 1.2, 1.4], "density": 700, "color_variant": "random", "object_type": "rectangular", "material_type": "paper", "material_properties": {"static_friction": 0.9, "dynamic_friction": 0.7, "restitution": 0.1}, "color_variants": ["red", "blue", "green", "yellow", "purple", "brown", "black"], "texture_variants": ["paper_smooth", "paper_textured", "leather_cover"]}, "phone_electronic": {"bbox": {"min": [-0.035, -0.07, -0.008], "max": [0.035, 0.07, 0.008]}, "scales": [0.9, 1.0, 1.1], "density": 1500, "color_variant": "random", "object_type": "rectangular", "material_type": "plastic_metal", "material_properties": {"static_friction": 0.7, "dynamic_friction": 0.5, "restitution": 0.1}, "color_variants": ["black", "white", "silver", "gold", "blue", "red"], "texture_variants": ["smooth_plastic", "metal_frame", "glass_screen"]}}, "_食物模拟物品_": {"bread_loaf_soft": {"bbox": {"min": [-0.08, -0.04, -0.03], "max": [0.08, 0.04, 0.03]}, "scales": [0.8, 1.0, 1.2], "density": 300, "color_variant": "brown", "object_type": "food", "material_type": "soft", "material_properties": {"static_friction": 0.8, "dynamic_friction": 0.6, "restitution": 0.05}, "texture_variants": ["bread_crust", "bread_soft"]}, "cheese_block_firm": {"bbox": {"min": [-0.04, -0.03, -0.02], "max": [0.04, 0.03, 0.02]}, "scales": [0.8, 1.0, 1.2], "density": 1000, "color_variant": "yellow", "object_type": "food", "material_type": "firm", "material_properties": {"static_friction": 0.7, "dynamic_friction": 0.5, "restitution": 0.1}, "color_variants": ["yellow", "white", "orange"], "texture_variants": ["cheese_smooth", "cheese_aged"]}}, "_特殊形状_挑战性物品": {"dumbbell_weight": {"bbox": {"min": [-0.08, -0.02, -0.02], "max": [0.08, 0.02, 0.02]}, "scales": [0.7, 1.0, 1.3], "density": 3000, "color_variant": "black", "object_type": "complex", "material_type": "metal", "material_properties": {"static_friction": 0.9, "dynamic_friction": 0.7, "restitution": 0.05}, "texture_variants": ["metal_textured", "rubber_grip"]}, "wrench_tool": {"bbox": {"min": [-0.1, -0.015, -0.01], "max": [0.1, 0.015, 0.01]}, "scales": [0.8, 1.0, 1.2], "density": 2500, "color_variant": "silver", "object_type": "tool", "material_type": "steel", "material_properties": {"static_friction": 0.8, "dynamic_friction": 0.6, "restitution": 0.1}, "texture_variants": ["steel_polished", "steel_textured"]}}}