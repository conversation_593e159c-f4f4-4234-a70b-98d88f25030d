{"pepsi_can": {"bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]}, "scales": [1.0], "density": 1000}, "opened_pepsi_can": {"bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]}, "scales": [1.0], "density": 50}, "coke_can": {"bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]}, "scales": [1.0], "density": 1000}, "opened_coke_can": {"bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]}, "scales": [1.0], "density": 50}, "sprite_can": {"bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]}, "scales": [1.0], "density": 1000}, "opened_sprite_can": {"bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]}, "scales": [1.0], "density": 50}, "7up_can": {"bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]}, "scales": [1.0], "density": 1000}, "opened_7up_can": {"bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]}, "scales": [1.0], "density": 50}, "fanta_can": {"bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]}, "scales": [1.0], "density": 1000}, "opened_fanta_can": {"bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]}, "scales": [1.0], "density": 50}, "redbull_can": {"bbox": {"min": [-0.02925, -0.066, -0.02925], "max": [0.02925, 0.066, 0.02925]}, "scales": [1.0], "density": 1000}, "opened_redbull_can": {"bbox": {"min": [-0.02925, -0.086, -0.02925], "max": [0.02925, 0.086, 0.02925]}, "scales": [1.0], "density": 50}, "blue_plastic_bottle": {"bbox": {"min": [-0.03005, -0.1015, -0.03025], "max": [0.03005, 0.1015, 0.03025]}, "scales": [1.0], "density": 50}, "apple": {"bbox": {"min": [-0.04, -0.04, -0.04], "max": [0.04, 0.04, 0.04]}, "scales": [1.0], "density": 800}, "orange": {"bbox": {"min": [-0.035, -0.035, -0.034], "max": [0.035, 0.035, 0.034]}, "scales": [1.0], "density": 800}, "sponge": {"bbox": {"min": [-0.035, -0.05, -0.018], "max": [0.035, 0.05, 0.018]}, "scales": [1.0], "density": 150}, "bridge_spoon_generated_modified": {"bbox": {"min": [-0.0173, -0.05, -0.013021], "max": [0.0173, 0.05, 0.013021]}, "scales": [1.0], "density": 1200}, "bridge_carrot_generated_modified": {"bbox": {"min": [-0.0575, -0.016, -0.019], "max": [0.0575, 0.016, 0.019]}, "scales": [1.0], "density": 700}, "green_cube_3cm": {"bbox": {"min": [-0.015, -0.015, -0.015], "max": [0.015, 0.015, 0.015]}, "scales": [1.0], "density": 1000}, "yellow_cube_3cm": {"bbox": {"min": [-0.015, -0.015, -0.015], "max": [0.015, 0.015, 0.015]}, "scales": [1.0], "density": 1000}, "eggplant": {"bbox": {"min": [-0.03915, -0.0187, -0.019], "max": [0.03915, 0.0187, 0.019]}, "scales": [1.0], "density": 400}}