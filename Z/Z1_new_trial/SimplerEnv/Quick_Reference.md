# SimplerEnv 快速参考卡片

## 🎯 可用物体类型

### 🍎 水果类
```python
fruits = [
    "apple",           # 苹果 - 红色，圆形
    "orange",          # 橙子 - 橙色，圆形  
    "eggplant"         # 茄子 - 紫色，椭圆形
]
```

### 🥤 饮料罐类
```python
cans = [
    "coke_can",        # 可乐罐 - 红色，圆柱形
    "pepsi_can",       # 百事罐 - 蓝色，圆柱形
    "opened_coke_can", # 开启的可乐罐 - 红色，较轻
    "opened_pepsi_can" # 开启的百事罐 - 蓝色，较轻
]
```

### 📦 几何体类
```python
cubes = [
    "green_cube_3cm",  # 绿色立方体 - 3cm边长
    "yellow_cube_3cm", # 黄色立方体 - 3cm边长
    "blue_cube_3cm"    # 蓝色立方体 - 3cm边长
]
```

### 🧽 日用品类
```python
daily_items = [
    "sponge",              # 海绵 - 黄色，软质
    "blue_plastic_bottle"  # 蓝色塑料瓶 - 圆柱形
]
```

### 🥄 餐具类
```python
utensils = [
    "bridge_spoon_generated_modified",   # 勺子 - 金属质感
    "bridge_carrot_generated_modified"   # 胡萝卜模型 - 橙色，细长
]
```

## 📷 相机类型对比

| 相机类型 | 视角 | 优点 | 缺点 | 推荐场景 |
|----------|------|------|------|----------|
| `overhead_camera` | 顶部俯视 | 能看到所有物体布局 | 无法看到物体侧面细节 | **多物体场景(推荐)** |
| `base_camera` | 侧面视角 | 能看到物体侧面 | 可能被桌子遮挡 | 单物体特写 |

## 🎛️ 快速配置模板

### 简单2物体场景
```python
main(
    target_object="apple",
    distractor_objects=["green_cube_3cm"],
    camera_type="overhead_camera",
    num_episodes=3
)
```

### 中等5物体场景
```python
main(
    target_object="orange",
    distractor_objects=["coke_can", "pepsi_can", "yellow_cube_3cm", "sponge"],
    camera_type="overhead_camera",
    num_episodes=5
)
```

### 复杂8物体场景
```python
main(
    target_object="eggplant",
    distractor_objects=[
        "opened_coke_can", "opened_pepsi_can", "green_cube_3cm",
        "yellow_cube_3cm", "apple", "orange", "bridge_carrot_generated_modified"
    ],
    camera_type="overhead_camera",
    num_episodes=3
)
```

### 自定义输出目录
```python
main(
    target_object="apple",
    distractor_objects=["coke_can", "sponge"],
    output_dir="/your/custom/path",
    image_prefix="my_scene",
    num_episodes=10
)
```

## 🚀 一键运行命令

### 基础运行
```bash
cd /home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv
python simpler_env_interface.py
```

### 查看生成的图像
```bash
ls -la /home/<USER>/claude/SpatialVLA/Z/Z_new_trial/*.png
```

## 🔧 故障排除

### 常见问题及解决方案

#### 问题1: 物体名称错误
```
❌ 错误: KeyError: 'invalid_object'
✅ 解决: 使用上面列出的正确物体名称
```

#### 问题2: 图像保存失败
```
❌ 错误: 无法保存图像
✅ 解决: 检查output_dir路径是否存在且有写权限
```

#### 问题3: 物体掉落
```
❌ 现象: 物体显示在桌子下面
✅ 解决: 确保enable_stable_placement=True (默认已启用)
```

#### 问题4: 相机视角不对
```
❌ 现象: 看不到物体或视角奇怪
✅ 解决: 使用camera_type="overhead_camera"
```

## 📊 性能参考

### 推荐配置
- **物体数量**: 2-8个 (超过8个可能不稳定)
- **场景数量**: 3-10个 (根据需要调整)
- **相机类型**: `overhead_camera` (最佳视角)
- **稳定放置**: `True` (防止物体掉落)

### 运行时间参考
- 2物体场景: ~10秒/场景
- 5物体场景: ~15秒/场景  
- 8物体场景: ~20秒/场景

## 💡 最佳实践

### 1. 物体选择建议
```python
# ✅ 推荐: 混合不同类型的物体
distractor_objects = ["coke_can", "green_cube_3cm", "apple", "sponge"]

# ❌ 避免: 只使用相似物体
distractor_objects = ["green_cube_3cm", "yellow_cube_3cm", "blue_cube_3cm"]
```

### 2. 场景数量建议
```python
# ✅ 推荐: 适中的场景数量
num_episodes = 5

# ❌ 避免: 过多场景(浪费时间)
num_episodes = 100
```

### 3. 输出管理建议
```python
# ✅ 推荐: 使用描述性前缀
image_prefix = "training_data_5objects"

# ❌ 避免: 无意义前缀
image_prefix = "test"
```

## 🎯 使用流程

1. **选择物体** → 从上面的物体列表中选择目标物体和干扰物体
2. **配置参数** → 设置相机类型、输出目录等参数
3. **运行生成** → 调用`main()`函数生成场景
4. **查看结果** → 检查生成的图像和控制台输出
5. **调整优化** → 根据结果调整参数重新生成

## 📞 技术支持

如果遇到问题：
1. 检查物体名称是否在支持列表中
2. 确认输出目录路径正确
3. 查看控制台错误信息
4. 尝试减少物体数量或场景数量
5. 使用默认参数进行测试

---

**🎉 现在您已经掌握了SimplerEnv的基本使用方法！开始创建您的多物体训练数据吧！**
