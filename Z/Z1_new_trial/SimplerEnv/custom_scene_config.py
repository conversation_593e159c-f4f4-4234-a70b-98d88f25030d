"""
自定义场景参数配置系统

这个模块定义了用于创建多样化训练场景的参数配置系统，
支持物体数量、位置、类型、颜色、空间布局等多种可配置参数。
"""

from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Optional, Union
import numpy as np
from enum import Enum


class ObjectType(Enum):
    """物体类型枚举"""
    CAN = "can"
    BOTTLE = "bottle"
    CUBE = "cube"
    FRUIT = "fruit"
    TOOL = "tool"
    CONTAINER = "container"


class ColorVariant(Enum):
    """颜色变体枚举"""
    RED = "red"
    BLUE = "blue"
    GREEN = "green"
    YELLOW = "yellow"
    ORANGE = "orange"
    PURPLE = "purple"
    WHITE = "white"
    BLACK = "black"


class LightingMode(Enum):
    """光照模式枚举"""
    DEFAULT = "default"
    BRIGHT = "bright"
    DIM = "dim"
    DRAMATIC = "dramatic"
    SOFT = "soft"


@dataclass
class ObjectConfig:
    """单个物体的配置参数"""
    object_type: ObjectType
    model_id: str
    color_variant: Optional[ColorVariant] = None
    scale: float = 1.0
    density: float = 1000.0
    position_range: Optional[Tuple[Tuple[float, float], Tuple[float, float]]] = None  # ((x_min, x_max), (y_min, y_max))
    rotation_range: Optional[Tuple[float, float]] = None  # (min_angle, max_angle) in radians
    texture_variant: Optional[str] = None
    material_properties: Optional[Dict[str, float]] = None


@dataclass
class SceneLayoutConfig:
    """场景布局配置"""
    table_height: float = 0.87
    workspace_bounds: Tuple[Tuple[float, float], Tuple[float, float]] = ((-0.6, 0.6), (-0.6, 0.6))  # 扩大工作空间以容纳更多物体
    object_spacing_min: float = 0.03  # 减小物体间距以容纳更多物体
    avoid_robot_radius: float = 0.12  # 减小避开机器人的半径
    target_area: Optional[Tuple[Tuple[float, float], Tuple[float, float]]] = None  # 目标区域


@dataclass
class LightingConfig:
    """光照配置"""
    mode: LightingMode = LightingMode.DEFAULT
    ambient_light: Tuple[float, float, float] = (0.3, 0.3, 0.3)
    directional_lights: List[Dict] = field(default_factory=list)
    enable_shadows: bool = True
    shadow_quality: str = "medium"  # low, medium, high


@dataclass
class CameraConfig:
    """相机配置"""
    add_segmentation: bool = True
    resolution: Tuple[int, int] = (224, 224)
    fov: float = 60.0
    near_plane: float = 0.01
    far_plane: float = 10.0


@dataclass
class RobotConfig:
    """机器人配置"""
    robot_type: str = "google_robot_static"
    init_position_range: Tuple[Tuple[float, float], Tuple[float, float]] = ((0.30, 0.40), (0.0, 0.2))
    init_rotation_range: Tuple[float, float] = (-0.09, 0.09)  # 绕z轴旋转范围
    control_frequency: int = 3
    simulation_frequency: int = 513


@dataclass
class CustomSceneConfig:
    """自定义场景的完整配置"""
    # 基本场景信息
    scene_name: str = "custom_diverse_scene"
    task_type: str = "pick_and_place"
    
    # 物体配置
    target_objects: List[ObjectConfig] = field(default_factory=list)
    distractor_objects: List[ObjectConfig] = field(default_factory=list)
    num_target_objects: Tuple[int, int] = (1, 1)  # (min, max)
    num_distractor_objects: Tuple[int, int] = (0, 3)  # (min, max)
    
    # 场景布局
    layout: SceneLayoutConfig = field(default_factory=SceneLayoutConfig)
    
    # 环境设置
    lighting: LightingConfig = field(default_factory=LightingConfig)
    camera: CameraConfig = field(default_factory=CameraConfig)
    robot: RobotConfig = field(default_factory=RobotConfig)
    
    # 多样性增强参数
    enable_texture_randomization: bool = True
    enable_lighting_randomization: bool = True
    enable_physics_randomization: bool = True
    enable_background_randomization: bool = False
    
    # 随机种子
    random_seed: Optional[int] = None


class SceneConfigGenerator:
    """场景配置生成器"""
    
    def __init__(self):
        self.object_database = self._load_object_database()
    
    def _load_object_database(self) -> Dict[str, Dict]:
        """加载物体数据库"""
        # 基于实际存在的物体模型创建数据库
        return {
            # 饮料罐类 - 使用实际存在的模型
            "coke_can": {
                "type": ObjectType.CAN,
                "color": ColorVariant.RED,
                "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
                "density": 1000,
                "scales": [0.8, 1.0, 1.2]
            },
            "pepsi_can": {
                "type": ObjectType.CAN,
                "color": ColorVariant.BLUE,
                "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
                "density": 1000,
                "scales": [0.8, 1.0, 1.2]
            },
            "sprite_can": {
                "type": ObjectType.CAN,
                "color": ColorVariant.GREEN,
                "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
                "density": 1000,
                "scales": [0.8, 1.0, 1.2]
            },
            "7up_can": {
                "type": ObjectType.CAN,
                "color": ColorVariant.GREEN,
                "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
                "density": 1000,
                "scales": [0.8, 1.0, 1.2]
            },
            "fanta_can": {
                "type": ObjectType.CAN,
                "color": ColorVariant.ORANGE,
                "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
                "density": 1000,
                "scales": [0.8, 1.0, 1.2]
            },
            "redbull_can": {
                "type": ObjectType.CAN,
                "color": ColorVariant.BLUE,
                "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
                "density": 1000,
                "scales": [0.8, 1.0, 1.2]
            },
            # 立方体类 - 使用实际存在的模型
            "green_cube_3cm": {
                "type": ObjectType.CUBE,
                "color": ColorVariant.GREEN,
                "bbox": {"min": [-0.015, -0.015, -0.015], "max": [0.015, 0.015, 0.015]},
                "density": 1000,
                "scales": [0.5, 1.0, 1.5, 2.0]
            },
            "yellow_cube_3cm": {
                "type": ObjectType.CUBE,
                "color": ColorVariant.YELLOW,
                "bbox": {"min": [-0.015, -0.015, -0.015], "max": [0.015, 0.015, 0.015]},
                "density": 1000,
                "scales": [0.5, 1.0, 1.5, 2.0]
            },
            # 水果类 - 使用实际存在的模型
            "apple": {
                "type": ObjectType.FRUIT,
                "color": ColorVariant.RED,
                "bbox": {"min": [-0.04, -0.04, -0.04], "max": [0.04, 0.04, 0.04]},
                "density": 200,
                "scales": [0.8, 1.0, 1.2]
            },
            "orange": {
                "type": ObjectType.FRUIT,
                "color": ColorVariant.ORANGE,
                "bbox": {"min": [-0.04, -0.04, -0.04], "max": [0.04, 0.04, 0.04]},
                "density": 200,
                "scales": [0.8, 1.0, 1.2]
            },
            # 瓶子类 - 使用实际存在的模型
            "blue_plastic_bottle": {
                "type": ObjectType.BOTTLE,
                "color": ColorVariant.BLUE,
                "bbox": {"min": [-0.03, -0.03, -0.08], "max": [0.03, 0.03, 0.08]},
                "density": 100,
                "scales": [0.8, 1.0, 1.2]
            },
            # 其他物体
            "sponge": {
                "type": ObjectType.TOOL,
                "color": ColorVariant.YELLOW,
                "bbox": {"min": [-0.04, -0.02, -0.01], "max": [0.04, 0.02, 0.01]},
                "density": 300,
                "scales": [0.8, 1.0, 1.2]
            }
        }
    
    def generate_random_config(self, 
                             num_objects_range: Tuple[int, int] = (3, 8),
                             include_distractors: bool = True,
                             seed: Optional[int] = None) -> CustomSceneConfig:
        """生成随机的场景配置"""
        if seed is not None:
            np.random.seed(seed)
        
        # 随机选择物体数量
        total_objects = np.random.randint(num_objects_range[0], num_objects_range[1] + 1)
        num_targets = np.random.randint(1, min(3, total_objects) + 1)
        num_distractors = total_objects - num_targets if include_distractors else 0
        
        # 随机选择目标物体
        target_objects = self._generate_random_objects(num_targets, is_target=True)
        
        # 随机选择干扰物体
        distractor_objects = self._generate_random_objects(num_distractors, is_target=False)
        
        # 随机光照配置
        lighting_config = self._generate_random_lighting()
        
        # 创建配置
        config = CustomSceneConfig(
            target_objects=target_objects,
            distractor_objects=distractor_objects,
            num_target_objects=(num_targets, num_targets),
            num_distractor_objects=(num_distractors, num_distractors),
            lighting=lighting_config,
            random_seed=seed
        )
        
        return config
    
    def _generate_random_objects(self, num_objects: int, is_target: bool = True) -> List[ObjectConfig]:
        """生成随机物体配置"""
        objects = []
        available_models = list(self.object_database.keys())
        
        for _ in range(num_objects):
            # 修复: 使用索引选择避免维度问题
            model_id = available_models[np.random.randint(0, len(available_models))]
            model_info = self.object_database[model_id]

            # 随机选择缩放
            scales = model_info.get("scales", [1.0])
            scale = scales[np.random.randint(0, len(scales))]
            
            # 随机位置范围
            if is_target:
                # 目标物体放在更容易到达的位置
                pos_range = ((-0.3, 0.1), (-0.1, 0.3))
            else:
                # 干扰物体可以放在更广的范围
                pos_range = ((-0.4, 0.4), (-0.4, 0.4))
            
            obj_config = ObjectConfig(
                object_type=model_info["type"],
                model_id=model_id,
                color_variant=model_info.get("color"),
                scale=scale,
                density=model_info["density"],
                position_range=pos_range,
                rotation_range=(0, 2 * np.pi)
            )
            objects.append(obj_config)
        
        return objects
    
    def _generate_random_lighting(self) -> LightingConfig:
        """生成随机光照配置"""
        # 修复: 使用索引选择避免维度问题
        lighting_modes = list(LightingMode)
        mode = lighting_modes[np.random.randint(0, len(lighting_modes))]

        # 根据模式调整环境光
        if mode == LightingMode.BRIGHT:
            ambient = (0.5, 0.5, 0.5)
        elif mode == LightingMode.DIM:
            ambient = (0.1, 0.1, 0.1)
        elif mode == LightingMode.SOFT:
            ambient = (0.4, 0.4, 0.4)
        else:
            ambient = (0.3, 0.3, 0.3)

        return LightingConfig(
            mode=mode,
            ambient_light=ambient,
            enable_shadows=bool(np.random.randint(0, 2))  # 修复: 使用randint替代choice
        )


# 预定义的场景配置模板
PRESET_CONFIGS = {
    "simple_pick": CustomSceneConfig(
        scene_name="simple_pick_scene",
        task_type="pick",
        num_target_objects=(1, 1),
        num_distractor_objects=(0, 2)
    ),

    "multi_object_pick": CustomSceneConfig(
        scene_name="multi_object_pick_scene",
        task_type="pick",
        num_target_objects=(2, 3),
        num_distractor_objects=(1, 4)
    ),

    "cluttered_scene": CustomSceneConfig(
        scene_name="cluttered_scene",
        task_type="pick_and_place",
        num_target_objects=(1, 2),
        num_distractor_objects=(3, 6),
        enable_texture_randomization=True,
        enable_lighting_randomization=True
    )
}


def create_diverse_training_configs(num_configs: int = 100, seed: int = 42) -> List[CustomSceneConfig]:
    """创建多样化的训练配置集合"""
    generator = SceneConfigGenerator()
    configs = []

    np.random.seed(seed)

    for i in range(num_configs):
        config_seed = np.random.randint(0, 10000)
        config = generator.generate_random_config(
            num_objects_range=(2, 8),
            include_distractors=True,
            seed=config_seed
        )
        config.scene_name = f"diverse_scene_{i:03d}"
        configs.append(config)

    return configs


def validate_config(config: CustomSceneConfig) -> List[str]:
    """验证配置的有效性，返回错误信息列表"""
    errors = []

    # 检查物体数量
    if config.num_target_objects[0] < 1:
        errors.append("至少需要一个目标物体")

    if config.num_target_objects[1] < config.num_target_objects[0]:
        errors.append("目标物体最大数量不能小于最小数量")

    if config.num_distractor_objects[1] < config.num_distractor_objects[0]:
        errors.append("干扰物体最大数量不能小于最小数量")

    # 检查工作空间边界
    x_bounds, y_bounds = config.layout.workspace_bounds
    if x_bounds[1] <= x_bounds[0] or y_bounds[1] <= y_bounds[0]:
        errors.append("工作空间边界设置无效")

    # 检查物体间距
    if config.layout.object_spacing_min <= 0:
        errors.append("物体间距必须大于0")

    return errors
