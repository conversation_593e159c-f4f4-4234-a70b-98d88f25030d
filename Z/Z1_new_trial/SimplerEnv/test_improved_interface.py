#!/usr/bin/env python3
"""
SimplerEnv改进接口测试脚本

测试新的分层接口和自定义位置功能
"""

import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))

from simpler_env_interface import (
    create_scene_simple,
    create_scene_custom, 
    create_scene_with_custom_positions,
    generate_grid_positions,
    generate_circle_positions,
    SceneConfigManager
)

def test_simple_interface():
    """测试简单接口"""
    print("🚀 测试简单接口")
    print("=" * 50)
    
    success, results, images = create_scene_simple(
        preset="desktop_picking",
        num_episodes=1,
        verbose=True
    )
    
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    if not success:
        print(f"错误: {results.get('error', '未知错误')}")
    
    return success

def test_custom_interface():
    """测试中级接口"""
    print("\n🎛️ 测试中级接口")
    print("=" * 50)
    
    success, results, images = create_scene_custom(
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="laboratory",
        objects=["apple", "orange", "coke_can"],
        num_episodes=1,
        verbose=True
    )
    
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    if not success:
        print(f"错误: {results.get('error', '未知错误')}")
    
    return success

def test_custom_positions():
    """测试自定义位置接口"""
    print("\n🎯 测试自定义位置接口")
    print("=" * 50)
    
    # 测试手动指定位置
    objects = ["apple", "orange"]
    positions = [
        (-0.25, 0.15, 0),    # apple在中心
        (-0.22, 0.18, 0),    # orange在右上
    ]
    
    success, results, images = create_scene_with_custom_positions(
        objects=objects,
        positions=positions,
        image_prefix="test_custom_pos",
        num_episodes=1,
        verbose=True
    )
    
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    if not success:
        print(f"错误: {results.get('error', '未知错误')}")
    
    return success

def test_layout_generators():
    """测试布局生成器"""
    print("\n📐 测试布局生成器")
    print("=" * 50)
    
    # 测试网格布局
    print("网格布局:")
    grid_pos = generate_grid_positions(4)
    for i, pos in enumerate(grid_pos):
        print(f"  位置{i+1}: {pos}")
    
    # 测试圆形布局
    print("\n圆形布局:")
    circle_pos = generate_circle_positions(3)
    for i, pos in enumerate(circle_pos):
        print(f"  位置{i+1}: {pos}")
    
    return True

def test_config_system():
    """测试配置系统"""
    print("\n📋 测试配置系统")
    print("=" * 50)
    
    # 列出预设配置
    print("可用预设:")
    presets = SceneConfigManager.list_presets()
    for preset, desc in presets.items():
        print(f"  • {preset}: {desc}")
    
    # 获取推荐物体
    print("\n推荐物体:")
    recommended = SceneConfigManager.recommend_objects("desktop_picking", 3)
    print(f"  桌面抓取(3个): {recommended}")
    
    return True

def main():
    """主测试函数"""
    print("🧪 SimplerEnv改进接口测试")
    print("=" * 60)
    
    tests = [
        ("配置系统", test_config_system),
        ("布局生成器", test_layout_generators),
        ("简单接口", test_simple_interface),
        ("中级接口", test_custom_interface), 
        ("自定义位置", test_custom_positions),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 开始测试: {test_name}")
            success = test_func()
            results[test_name] = success
            print(f"✅ {test_name} 测试完成: {'成功' if success else '失败'}")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed_tests}/{total_tests} 测试通过")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
