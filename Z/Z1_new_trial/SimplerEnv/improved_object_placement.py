#!/usr/bin/env python3
"""
改进的物体放置系统

解决物体掉落到桌子下面的问题，确保所有物体都稳定地放置在桌面上。
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")

def test_improved_placement():
    """测试改进的物体放置系统"""
    print("=" * 60)
    print("SimplerEnv改进物体放置系统测试")
    print("=" * 60)
    
    try:
        from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
        import sapien.core as sapien
        from transforms3d.euler import euler2quat
        
        print("✓ 成功导入环境模块")
        
        # 测试场景配置
        test_scenarios = [
            {
                "name": "改进8物体测试",
                "target": "eggplant",
                "distractors": ["opened_coke_can", "opened_pepsi_can", "green_cube_3cm", 
                              "yellow_cube_3cm", "apple", "orange", "bridge_carrot_generated_modified"],
                "description": "茄子 + 7个干扰物体 (改进放置)"
            }
        ]
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n{'='*50}")
            print(f"场景 {i+1}: {scenario['name']}")
            print(f"描述: {scenario['description']}")
            print(f"{'='*50}")
            
            try:
                # 创建改进的测试环境
                class ImprovedPlacementEnv(GraspSingleCustomInSceneEnv):
                    def __init__(self, target_id, distractor_ids, **kwargs):
                        kwargs.setdefault("model_ids", [target_id])
                        kwargs.setdefault("distractor_model_ids", distractor_ids)
                        kwargs.setdefault("robot", "google_robot_static")
                        super().__init__(**kwargs)
                    
                    def reset(self, seed=None, options=None):
                        if options is None:
                            options = dict()
                        options = options.copy()
                        options["distractor_model_ids"] = self.distractor_model_ids
                        return super().reset(seed=seed, options=options)
                    
                    def _initialize_actors(self):
                        """改进的物体初始化方法"""
                        if not hasattr(self, 'obj') or self.obj is None:
                            print("警告：没有目标物体")
                            return
                        
                        # 收集所有物体
                        all_objects = [self.obj]
                        if hasattr(self, 'distractor_objs') and self.distractor_objs:
                            all_objects.extend(self.distractor_objs)
                        
                        print(f"开始改进初始化 {len(all_objects)} 个物体")
                        
                        # 扩大工作空间边界，确保有足够空间
                        workspace_bounds = ((-0.45, -0.25), (-0.05, 0.45))  # 扩大边界
                        min_spacing = 0.12  # 减小最小间距，但仍保持合理距离
                        
                        # 使用网格布局确保稳定放置
                        grid_cols = 3  # 3列
                        grid_rows = int(np.ceil(len(all_objects) / grid_cols))
                        
                        # 计算网格间距
                        x_range = workspace_bounds[0][1] - workspace_bounds[0][0]
                        y_range = workspace_bounds[1][1] - workspace_bounds[1][0]
                        x_spacing = x_range / (grid_cols + 1)
                        y_spacing = y_range / (grid_rows + 1)
                        
                        print(f"使用 {grid_rows}x{grid_cols} 网格布局")
                        print(f"网格间距: x={x_spacing:.3f}, y={y_spacing:.3f}")
                        
                        # 为每个物体分配网格位置
                        for i, obj in enumerate(all_objects):
                            if obj is None:
                                print(f"警告：第{i}个物体为None，跳过")
                                continue
                            
                            # 计算网格位置
                            row = i // grid_cols
                            col = i % grid_cols
                            
                            # 基础网格位置
                            base_x = workspace_bounds[0][0] + (col + 1) * x_spacing
                            base_y = workspace_bounds[1][0] + (row + 1) * y_spacing
                            
                            # 添加小的随机偏移避免完全对齐
                            offset_x = self._episode_rng.uniform(-0.03, 0.03)
                            offset_y = self._episode_rng.uniform(-0.03, 0.03)
                            
                            x = base_x + offset_x
                            y = base_y + offset_y
                            z = self.scene_table_height + 0.15  # 较低的初始高度，减少弹跳
                            
                            # 确保在边界内
                            x = np.clip(x, workspace_bounds[0][0] + 0.05, workspace_bounds[0][1] - 0.05)
                            y = np.clip(y, workspace_bounds[1][0] + 0.05, workspace_bounds[1][1] - 0.05)
                            
                            # 小的随机旋转
                            rot_angle = self._episode_rng.uniform(0, 2 * np.pi)
                            q = euler2quat(0, 0, rot_angle)
                            
                            # 设置物体位置
                            print(f"放置物体 {obj.name} 在网格位置 ({row},{col}): ({x:.2f}, {y:.2f}, {z:.2f})")
                            obj.set_pose(sapien.Pose([x, y, z], q))
                            
                            # 锁定x和y轴旋转，让物体垂直落下
                            obj.lock_motion(0, 0, 0, 1, 1, 0)
                        
                        # 将机器人移到远处避免碰撞
                        self.agent.robot.set_pose(sapien.Pose([-10, 0, 0]))
                        
                        # 让物体稳定落到桌面 - 分阶段稳定
                        print("第一阶段稳定...")
                        self._settle(0.8)  # 更长的稳定时间
                        
                        # 解锁物体运动
                        for obj in all_objects:
                            if obj is not None:
                                obj.lock_motion(0, 0, 0, 0, 0, 0)
                                # 确保物体不会休眠
                                obj.set_pose(obj.pose)
                                obj.set_velocity(np.zeros(3))
                                obj.set_angular_velocity(np.zeros(3))
                        
                        # 第二阶段稳定
                        print("第二阶段稳定...")
                        self._settle(1.0)
                        
                        # 检查物体是否需要额外稳定时间
                        total_lin_vel, total_ang_vel = 0.0, 0.0
                        for obj in all_objects:
                            if obj is not None:
                                total_lin_vel += np.linalg.norm(obj.velocity)
                                total_ang_vel += np.linalg.norm(obj.angular_velocity)
                        
                        print(f"物体运动状态: 线速度={total_lin_vel:.6f}, 角速度={total_ang_vel:.6f}")
                        
                        if total_lin_vel > 1e-3 or total_ang_vel > 1e-2:
                            print("需要额外稳定时间...")
                            self._settle(2.0)  # 更长的额外稳定时间
                        
                        # 最终检查物体位置
                        print("最终物体位置:")
                        for j, obj in enumerate(all_objects):
                            if obj is not None:
                                pos = obj.pose.p
                                print(f"  {j+1}. {obj.name}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f})")
                                
                                # 检查是否掉落
                                if pos[2] < self.scene_table_height - 0.1:
                                    print(f"    ⚠ 警告: {obj.name} 可能掉落了！")
                        
                        print("改进物体初始化完成")
                
                # 创建环境
                env = ImprovedPlacementEnv(
                    target_id=scenario["target"],
                    distractor_ids=scenario["distractors"],
                    obs_mode="image",
                    control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",
                    render_mode="rgb_array"
                )
                
                print(f"✓ 成功创建环境")
                
                # 重置环境
                obs, info = env.reset()
                print(f"✓ 成功重置环境")
                
                # 统计物体
                target_count = 1
                distractor_count = len(getattr(env.unwrapped, 'distractor_objs', []))
                total_objects = target_count + distractor_count
                
                print(f"物体统计:")
                print(f"  - 目标物体: {target_count} ({scenario['target']})")
                print(f"  - 干扰物体: {distractor_count}")
                print(f"  - 总物体数: {total_objects}")
                
                # 获取场景中的所有actors
                actors = env.unwrapped._scene.get_all_actors()
                object_actors = [a for a in actors if a.name != 'arena']
                print(f"  - 场景物体: {len(object_actors)}")
                
                # 检查物体位置
                table_height = env.unwrapped.scene_table_height
                on_table_count = 0
                below_table_count = 0
                
                print(f"物体位置检查 (桌面高度: {table_height:.2f}):")
                for j, actor in enumerate(object_actors):
                    pos = actor.pose.p
                    if pos[2] >= table_height - 0.05:  # 在桌面上或附近
                        on_table_count += 1
                        status = "✓ 在桌面上"
                    else:
                        below_table_count += 1
                        status = "✗ 掉落了"
                    print(f"  {j+1}. {actor.name}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f}) {status}")
                
                print(f"位置统计: 桌面上={on_table_count}, 掉落={below_table_count}")
                
                # 保存相机图像
                if 'image' in obs:
                    for camera_name in obs['image'].keys():
                        camera_data = obs['image'][camera_name]
                        
                        # 获取图像数据
                        rgb_img = None
                        for img_key in ['Color', 'rgb', 'color']:
                            if img_key in camera_data:
                                rgb_img = camera_data[img_key]
                                break
                        
                        if rgb_img is not None:
                            # 确保图像格式正确
                            if rgb_img.dtype != np.uint8:
                                rgb_img = (rgb_img * 255).astype(np.uint8)
                            
                            output_path = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/improved_placement_{scenario['name']}_{camera_name}.png"
                            cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                            print(f"  - 保存{camera_name}图像: {output_path}")
                
                env.close()
                print(f"✓ {scenario['name']} 测试完成")
                
                # 返回结果
                success = below_table_count == 0
                return success, on_table_count, below_table_count
                
            except Exception as e:
                print(f"✗ {scenario['name']} 测试失败: {e}")
                import traceback
                traceback.print_exc()
                return False, 0, 0
        
    except Exception as e:
        print(f"\n✗ 改进物体放置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 0


if __name__ == "__main__":
    print("SimplerEnv改进物体放置系统测试")
    success, on_table, below_table = test_improved_placement()
    
    if success:
        print(f"\n🎉 改进物体放置测试成功！")
        print(f"   - 所有 {on_table} 个物体都稳定地放置在桌面上")
        print(f"   - 没有物体掉落")
    else:
        print(f"\n⚠ 改进物体放置测试部分成功")
        print(f"   - 桌面上: {on_table} 个物体")
        print(f"   - 掉落: {below_table} 个物体")
        print(f"   - 需要进一步调整放置策略")
