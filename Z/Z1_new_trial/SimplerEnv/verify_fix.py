#!/usr/bin/env python3
"""
验证修复后的物体数量问题

检查新生成的图像是否包含正确数量的物体
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")

def verify_object_count():
    """验证修复后的物体数量"""
    print("🔍 验证修复后的物体数量")
    print("=" * 60)
    
    try:
        from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
        import sapien.core as sapien
        from transforms3d.euler import euler2quat
        
        # 测试场景配置
        test_scenarios = [
            {
                "name": "4物体场景",
                "target": "apple",
                "distractors": ["coke_can", "green_cube_3cm", "orange"],
                "expected": 4
            },
            {
                "name": "5物体场景", 
                "target": "orange",
                "distractors": ["pepsi_can", "yellow_cube_3cm", "sponge", "bridge_spoon_generated_modified"],
                "expected": 5
            }
        ]
        
        # 使用修复后的环境类
        from simpler_env_interface import create_stable_environment
        
        results = []
        
        for scenario in test_scenarios:
            print(f"\n🎯 测试 {scenario['name']}")
            print(f"   - 目标物体: {scenario['target']}")
            print(f"   - 干扰物体: {scenario['distractors']}")
            print(f"   - 期望总数: {scenario['expected']}")
            
            # 创建环境
            EnvClass = create_stable_environment(
                scenario['target'], 
                scenario['distractors'], 
                "google_robot_static"
            )
            
            env = EnvClass(
                target_id=scenario['target'],
                distractor_ids=scenario['distractors'],
                obs_mode="image",
                control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",
                render_mode="rgb_array"
            )
            
            # 重置环境
            obs, _ = env.reset()
            
            # 统计物体
            target_count = 1 if hasattr(env.unwrapped, 'obj') and env.unwrapped.obj else 0
            distractor_count = len([obj for obj in getattr(env.unwrapped, 'distractor_objs', []) if obj is not None])
            env_total = target_count + distractor_count
            
            # 场景物体统计
            actors = env.unwrapped._scene.get_all_actors()
            object_actors = [a for a in actors if a.name != 'arena']
            scene_total = len(object_actors)
            
            # 可见性统计
            table_height = env.unwrapped.scene_table_height
            visible_count = 0
            on_table_count = 0
            
            object_details = []
            for actor in object_actors:
                pos = actor.pose.p
                on_table = pos[2] >= table_height - 0.05
                visible = (-0.35 <= pos[0] <= -0.15) and (0.05 <= pos[1] <= 0.35)
                
                if on_table:
                    on_table_count += 1
                if on_table and visible:
                    visible_count += 1
                
                object_details.append({
                    "name": actor.name,
                    "position": [float(pos[0]), float(pos[1]), float(pos[2])],
                    "on_table": on_table,
                    "visible": visible
                })
            
            # 保存验证图像
            if 'image' in obs and 'overhead_camera' in obs['image']:
                camera_data = obs['image']['overhead_camera']
                if 'Color' in camera_data:
                    rgb_img = camera_data['Color']
                    if rgb_img.dtype != np.uint8:
                        rgb_img = (rgb_img * 255).astype(np.uint8)
                    
                    output_path = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/verify_{scenario['name'].replace('物体场景', 'objects')}_fixed.png"
                    cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                    print(f"   💾 验证图像: {output_path}")
            
            # 结果分析
            success = visible_count == scenario['expected']
            
            print(f"   📊 统计结果:")
            print(f"      - 环境物体数: {env_total}")
            print(f"      - 场景物体数: {scene_total}")
            print(f"      - 桌面物体数: {on_table_count}")
            print(f"      - 可见物体数: {visible_count}")
            print(f"      - 期望物体数: {scenario['expected']}")
            print(f"      - 验证结果: {'✅ 成功' if success else '❌ 失败'}")
            
            if not success:
                print(f"   🔍 详细物体信息:")
                for obj in object_details:
                    status_parts = []
                    if obj['on_table']:
                        status_parts.append("桌面上")
                    else:
                        status_parts.append("掉落")
                    if obj['visible']:
                        status_parts.append("可见")
                    else:
                        status_parts.append("视野外")
                    status = " | ".join(status_parts)
                    print(f"      - {obj['name']}: ({obj['position'][0]:.2f}, {obj['position'][1]:.2f}, {obj['position'][2]:.2f}) [{status}]")
            
            results.append({
                "scenario": scenario['name'],
                "expected": scenario['expected'],
                "env_total": env_total,
                "scene_total": scene_total,
                "visible_count": visible_count,
                "success": success,
                "objects": object_details
            })
            
            env.close()
        
        # 总结
        print(f"\n📊 验证总结:")
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        print(f"   - 测试场景数: {total_count}")
        print(f"   - 成功场景数: {success_count}")
        print(f"   - 成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == total_count:
            print(f"   🎉 所有测试通过！物体数量问题已修复")
        else:
            print(f"   ⚠️ 仍有 {total_count - success_count} 个场景存在问题")
        
        return results
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def compare_before_after():
    """对比修复前后的图像"""
    print(f"\n🔄 对比修复前后的图像")
    print("=" * 40)
    
    # 检查修复前的图像
    old_5objects = "/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/custom_5objects_episode2_overhead_camera.png"
    new_5objects = "/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/verify_5objects_fixed.png"
    
    if os.path.exists(old_5objects):
        print(f"📸 修复前 (5物体): {old_5objects}")
        print(f"   - 问题: 声称5个物体，实际只有3个可见")
    
    if os.path.exists(new_5objects):
        print(f"📸 修复后 (5物体): {new_5objects}")
        print(f"   - 期望: 5个物体都应该可见")
    
    # 检查4物体场景
    old_4objects = "/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/custom_scene_episode3_overhead_camera.png"
    new_4objects = "/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/verify_4objects_fixed.png"
    
    if os.path.exists(old_4objects):
        print(f"📸 修复前 (4物体): {old_4objects}")
        print(f"   - 状态: 4个物体都可见 (这个是正常的)")
    
    if os.path.exists(new_4objects):
        print(f"📸 修复后 (4物体): {new_4objects}")
        print(f"   - 期望: 4个物体都应该可见")


if __name__ == "__main__":
    # 运行验证
    results = verify_object_count()
    
    # 对比修复前后
    compare_before_after()
    
    if results:
        print(f"\n💡 修复说明:")
        print(f"   - 增强了物体稳定放置算法")
        print(f"   - 添加了位置偏移检测和强制重置")
        print(f"   - 改进了相机视野范围检查")
        print(f"   - 增加了多步骤验证和修正机制")
        print(f"   - 确保所有物体都在桌面上且在相机视野内")
