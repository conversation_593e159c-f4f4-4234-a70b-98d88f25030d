#!/usr/bin/env python3
"""
测试修复后的SimplerEnv对象放置机制

这个脚本用于验证修复后的自定义环境是否能正确显示物体。
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")

def test_simple_diverse_env():
    """测试简化的多样化环境"""
    print("=" * 60)
    print("测试SimplerEnv对象放置修复效果")
    print("=" * 60)
    
    try:
        # 导入环境
        import mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env
        import gymnasium as gym
        
        print("✓ 成功导入环境模块")
        
        # 创建环境
        env_id = "SimpleDiverseTestEnv-v1"
        print(f"创建环境: {env_id}")
        
        env = gym.make(
            env_id,
            obs_mode="rgbd",
            control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",
            render_mode="rgb_array"
        )
        
        print("✓ 成功创建环境")
        
        # 重置环境
        print("\n重置环境...")
        obs, info = env.reset()
        
        print("✓ 成功重置环境")
        print(f"观察空间键: {list(obs.keys())}")
        
        # 检查场景信息
        if 'scene_config' in info:
            scene_info = info['scene_config']
            print(f"\n场景配置信息:")
            print(f"  - 场景名称: {scene_info.get('scene_name', 'N/A')}")
            print(f"  - 目标物体数量: {scene_info.get('num_target_objects', 0)}")
            print(f"  - 干扰物体数量: {scene_info.get('num_distractor_objects', 0)}")
            print(f"  - 总物体数量: {scene_info.get('total_objects', 0)}")
        
        # 获取图像
        if 'image' in obs:
            print(f"\n图像观察:")
            for camera_name, camera_data in obs['image'].items():
                if 'rgb' in camera_data:
                    rgb_shape = camera_data['rgb'].shape
                    print(f"  - {camera_name} RGB: {rgb_shape}")
                    
                    # 保存图像
                    rgb_img = (camera_data['rgb'] * 255).astype(np.uint8)
                    output_path = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/test_fixed_objects_{camera_name}.png"
                    cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                    print(f"    保存图像到: {output_path}")
        
        # 获取actor信息
        actors = env.unwrapped._scene.get_all_actors()
        print(f"\n场景中的所有actors ({len(actors)} 个):")
        for i, actor in enumerate(actors):
            pos = actor.pose.p
            print(f"  {i+1}. {actor.name}: 位置({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # 执行几步动作
        print(f"\n执行测试动作...")
        for step in range(5):
            action = env.action_space.sample()
            obs, reward, done, truncated, info = env.step(action)
            print(f"  步骤 {step+1}: reward={reward:.3f}, done={done}, truncated={truncated}")
            
            if done or truncated:
                break
        
        # 最终图像
        if 'image' in obs:
            for camera_name, camera_data in obs['image'].items():
                if 'rgb' in camera_data:
                    rgb_img = (camera_data['rgb'] * 255).astype(np.uint8)
                    output_path = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/test_fixed_objects_{camera_name}_final.png"
                    cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                    print(f"    保存最终图像到: {output_path}")
        
        env.close()
        print("\n✓ 测试完成！")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_database():
    """测试模型数据库是否正确"""
    print("\n" + "=" * 60)
    print("测试模型数据库")
    print("=" * 60)
    
    try:
        from mani_skill2_real2sim.utils.io_utils import load_json
        
        # 加载模型数据库
        model_json_path = Path(__file__).parent / "ManiSkill2_real2sim" / "data" / "custom" / "info_custom_diverse_objects_v1.json"
        model_db = load_json(model_json_path)
        
        print(f"✓ 成功加载模型数据库: {model_json_path}")
        print(f"数据库中的模型数量: {len(model_db)}")
        
        # 检查模型文件是否存在
        models_dir = Path(__file__).parent / "ManiSkill2_real2sim" / "data" / "custom" / "models"
        
        print(f"\n检查模型文件:")
        missing_models = []
        existing_models = []
        
        for model_id in model_db.keys():
            if model_id.startswith("_") or model_id in ["comment", "version", "created_for"]:
                continue
                
            model_dir = models_dir / model_id
            collision_file = model_dir / "collision.obj"
            
            visual_files = [
                model_dir / "textured.obj",
                model_dir / "textured.dae",
                model_dir / "textured.glb"
            ]
            
            has_collision = collision_file.exists()
            has_visual = any(f.exists() for f in visual_files)
            
            if has_collision and has_visual:
                existing_models.append(model_id)
                print(f"  ✓ {model_id}: 完整")
            else:
                missing_models.append(model_id)
                print(f"  ✗ {model_id}: 缺少文件 (collision: {has_collision}, visual: {has_visual})")
        
        print(f"\n总结:")
        print(f"  - 完整模型: {len(existing_models)} 个")
        print(f"  - 缺失模型: {len(missing_models)} 个")
        
        if missing_models:
            print(f"  - 缺失的模型: {missing_models}")
        
        return len(missing_models) == 0
        
    except Exception as e:
        print(f"\n✗ 模型数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("SimplerEnv对象放置修复验证")
    print("=" * 60)
    
    # 测试模型数据库
    db_ok = test_model_database()
    
    # 测试环境
    env_ok = test_simple_diverse_env()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"  - 模型数据库: {'✓ 通过' if db_ok else '✗ 失败'}")
    print(f"  - 环境测试: {'✓ 通过' if env_ok else '✗ 失败'}")
    
    if db_ok and env_ok:
        print("\n🎉 所有测试通过！SimplerEnv对象放置机制修复成功！")
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
