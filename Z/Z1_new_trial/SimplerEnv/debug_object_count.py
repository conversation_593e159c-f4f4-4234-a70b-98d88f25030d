#!/usr/bin/env python3
"""
调试物体数量不匹配问题

分析为什么声称的物体数量与实际可见物体数量不符。
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")

def debug_object_count():
    """调试物体数量问题"""
    print("🔍 调试物体数量不匹配问题")
    print("=" * 60)
    
    try:
        from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
        import sapien.core as sapien
        from transforms3d.euler import euler2quat
        
        # 测试场景：5物体场景
        target_object = "orange"
        distractor_objects = ["pepsi_can", "yellow_cube_3cm", "sponge", "bridge_spoon_generated_modified"]
        expected_total = len(distractor_objects) + 1  # 5个物体
        
        print(f"🎯 测试配置:")
        print(f"   - 目标物体: {target_object}")
        print(f"   - 干扰物体: {distractor_objects}")
        print(f"   - 期望总数: {expected_total}")
        print()
        
        # 创建增强调试环境
        class DebugObjectCountEnv(GraspSingleCustomInSceneEnv):
            def __init__(self, target_id, distractor_ids, **kwargs):
                kwargs.setdefault("model_ids", [target_id])
                kwargs.setdefault("distractor_model_ids", distractor_ids)
                kwargs.setdefault("robot", "google_robot_static")
                super().__init__(**kwargs)
            
            def reset(self, seed=None, options=None):
                if options is None:
                    options = dict()
                options = options.copy()
                options["distractor_model_ids"] = self.distractor_model_ids
                return super().reset(seed=seed, options=options)
            
            def _initialize_actors(self):
                """增强调试的物体初始化"""
                from transforms3d.euler import euler2quat
                import sapien.core as sapien
                
                print("🔧 开始物体初始化调试")
                
                # 检查目标物体
                print(f"📦 目标物体检查:")
                if hasattr(self, 'obj') and self.obj is not None:
                    print(f"   ✅ 目标物体存在: {self.obj.name}")
                else:
                    print(f"   ❌ 目标物体不存在")
                
                # 检查干扰物体
                print(f"📦 干扰物体检查:")
                if hasattr(self, 'distractor_objs'):
                    print(f"   - distractor_objs 属性存在")
                    print(f"   - distractor_objs 类型: {type(self.distractor_objs)}")
                    print(f"   - distractor_objs 长度: {len(self.distractor_objs) if self.distractor_objs else 0}")
                    
                    if self.distractor_objs:
                        for i, obj in enumerate(self.distractor_objs):
                            if obj is not None:
                                print(f"   ✅ 干扰物体 {i+1}: {obj.name}")
                            else:
                                print(f"   ❌ 干扰物体 {i+1}: None")
                    else:
                        print(f"   ❌ distractor_objs 为空")
                else:
                    print(f"   ❌ distractor_objs 属性不存在")
                
                # 收集所有物体
                all_objects = []
                if hasattr(self, 'obj') and self.obj is not None:
                    all_objects.append(self.obj)
                
                if hasattr(self, 'distractor_objs') and self.distractor_objs:
                    all_objects.extend([obj for obj in self.distractor_objs if obj is not None])
                
                print(f"📊 物体收集结果:")
                print(f"   - 收集到的物体数量: {len(all_objects)}")
                print(f"   - 期望物体数量: {expected_total}")
                
                if len(all_objects) != expected_total:
                    print(f"   ⚠️ 物体数量不匹配！")
                    
                    # 详细分析原因
                    print(f"🔍 详细分析:")
                    print(f"   - 目标物体数量: {1 if hasattr(self, 'obj') and self.obj else 0}")
                    print(f"   - 干扰物体数量: {len([obj for obj in getattr(self, 'distractor_objs', []) if obj is not None])}")
                    print(f"   - 期望干扰物体数量: {len(distractor_objects)}")
                    
                    # 检查模型ID配置
                    if hasattr(self, 'distractor_model_ids'):
                        print(f"   - 配置的干扰物体ID: {self.distractor_model_ids}")
                    
                    if hasattr(self, 'selected_distractor_model_ids'):
                        print(f"   - 选择的干扰物体ID: {self.selected_distractor_model_ids}")
                
                # 使用更大的工作空间确保所有物体都在相机视野内
                safe_positions = [
                    (-0.25, 0.15),  # 中心区域位置
                    (-0.20, 0.15),
                    (-0.30, 0.15),
                    (-0.25, 0.20),
                    (-0.25, 0.10),
                    (-0.20, 0.20),
                    (-0.30, 0.20),
                    (-0.20, 0.10),
                ]
                
                # 将机器人移到远处
                self.agent.robot.set_pose(sapien.Pose([-10, 0, 0]))
                
                # 逐个放置物体
                print(f"🎯 开始放置物体:")
                for i, obj in enumerate(all_objects):
                    if obj is None:
                        print(f"   ⚠️ 第{i+1}个物体为None，跳过")
                        continue
                    
                    # 使用安全位置
                    if i < len(safe_positions):
                        x, y = safe_positions[i]
                    else:
                        x = -0.25 + (i % 3) * 0.05
                        y = 0.15 + (i // 3) * 0.05
                    
                    # 直接放在桌面上
                    z = self.scene_table_height + 0.02
                    q = euler2quat(0, 0, 0)
                    
                    print(f"   📍 放置 {obj.name} 在位置: ({x:.2f}, {y:.2f}, {z:.2f})")
                    obj.set_pose(sapien.Pose([x, y, z], q))
                    obj.set_damping(0.5, 0.5)
                    self._settle(0.3)
                    
                    # 检查放置后位置
                    pos_after = obj.pose.p
                    print(f"      实际位置: ({pos_after[0]:.2f}, {pos_after[1]:.2f}, {pos_after[2]:.2f})")
                    
                    if pos_after[2] < self.scene_table_height - 0.05:
                        print(f"      ⚠️ 物体可能掉落，重新调整")
                        obj.set_pose(sapien.Pose([x, y, self.scene_table_height + 0.05], q))
                        obj.set_velocity(np.zeros(3))
                        obj.set_angular_velocity(np.zeros(3))
                        self._settle(0.5)
                
                # 最终稳定
                self._settle(1.0)
                
                # 最终检查
                print(f"🏁 最终物体位置检查:")
                for i, obj in enumerate(all_objects):
                    if obj is not None:
                        pos = obj.pose.p
                        on_table = pos[2] >= self.scene_table_height - 0.05
                        status = "✅ 桌面上" if on_table else "❌ 掉落"
                        print(f"   {i+1}. {obj.name}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f}) {status}")
                
                # 记录目标物体高度
                if hasattr(self, 'obj') and self.obj:
                    self.obj_height_after_settle = self.obj.pose.p[2]
                
                print("🔧 物体初始化调试完成")
        
        # 创建环境
        env = DebugObjectCountEnv(
            target_id=target_object,
            distractor_ids=distractor_objects,
            obs_mode="image",
            control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",
            render_mode="rgb_array"
        )
        
        print("✅ 环境创建成功")
        
        # 重置环境
        obs, _ = env.reset()
        print("✅ 环境重置成功")
        
        # 详细统计分析
        print(f"\n📊 详细统计分析:")
        
        # 方法1：通过环境属性统计
        target_count = 1 if hasattr(env.unwrapped, 'obj') and env.unwrapped.obj else 0
        distractor_count = len([obj for obj in getattr(env.unwrapped, 'distractor_objs', []) if obj is not None])
        env_total = target_count + distractor_count
        
        print(f"方法1 - 环境属性统计:")
        print(f"   - 目标物体: {target_count}")
        print(f"   - 干扰物体: {distractor_count}")
        print(f"   - 总计: {env_total}")
        
        # 方法2：通过场景actors统计
        actors = env.unwrapped._scene.get_all_actors()
        object_actors = [a for a in actors if a.name != 'arena']
        scene_total = len(object_actors)
        
        print(f"方法2 - 场景actors统计:")
        print(f"   - 场景物体总数: {scene_total}")
        print(f"   - 物体列表:")
        for j, actor in enumerate(object_actors):
            pos = actor.pose.p
            on_table = pos[2] >= env.unwrapped.scene_table_height - 0.05
            visible = (-0.4 <= pos[0] <= -0.1) and (0.05 <= pos[1] <= 0.35)  # 相机视野范围
            status_parts = []
            if on_table:
                status_parts.append("桌面上")
            else:
                status_parts.append("掉落")
            if visible:
                status_parts.append("可见")
            else:
                status_parts.append("视野外")
            status = " | ".join(status_parts)
            print(f"      {j+1}. {actor.name}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f}) [{status}]")
        
        # 方法3：统计可见物体
        table_height = env.unwrapped.scene_table_height
        visible_count = 0
        on_table_count = 0
        
        for actor in object_actors:
            pos = actor.pose.p
            on_table = pos[2] >= table_height - 0.05
            visible = (-0.4 <= pos[0] <= -0.1) and (0.05 <= pos[1] <= 0.35)
            
            if on_table:
                on_table_count += 1
            if on_table and visible:
                visible_count += 1
        
        print(f"方法3 - 可见性统计:")
        print(f"   - 桌面上物体: {on_table_count}")
        print(f"   - 可见物体: {visible_count}")
        
        # 保存调试图像
        if 'image' in obs and 'overhead_camera' in obs['image']:
            camera_data = obs['image']['overhead_camera']
            if 'Color' in camera_data:
                rgb_img = camera_data['Color']
                if rgb_img.dtype != np.uint8:
                    rgb_img = (rgb_img * 255).astype(np.uint8)
                
                output_path = "/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/debug_object_count.png"
                cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                print(f"💾 调试图像保存: {output_path}")
        
        # 分析结果
        print(f"\n🔍 问题分析:")
        print(f"   - 期望物体数: {expected_total}")
        print(f"   - 环境统计数: {env_total}")
        print(f"   - 场景物体数: {scene_total}")
        print(f"   - 可见物体数: {visible_count}")
        
        if env_total != expected_total:
            print(f"   ❌ 环境物体创建问题：期望{expected_total}个，实际{env_total}个")
        elif scene_total != expected_total:
            print(f"   ❌ 场景物体统计问题：期望{expected_total}个，场景{scene_total}个")
        elif visible_count != expected_total:
            print(f"   ❌ 物体可见性问题：期望{expected_total}个，可见{visible_count}个")
        else:
            print(f"   ✅ 所有统计都正确")
        
        env.close()
        
        return {
            "expected": expected_total,
            "env_total": env_total,
            "scene_total": scene_total,
            "visible_count": visible_count,
            "success": visible_count == expected_total
        }
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    result = debug_object_count()
    
    if result:
        if result["success"]:
            print(f"\n🎉 调试成功！所有{result['expected']}个物体都正确可见")
        else:
            print(f"\n⚠️ 发现问题：期望{result['expected']}个物体，但只有{result['visible_count']}个可见")
            print(f"   建议检查物体创建和放置逻辑")
    else:
        print(f"\n❌ 调试过程出现错误")
