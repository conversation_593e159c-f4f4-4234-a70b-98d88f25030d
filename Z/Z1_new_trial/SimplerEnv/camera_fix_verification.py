#!/usr/bin/env python3
"""
相机视角修复验证

修复相机视角问题，确保使用正确的overhead_camera视角来显示多物体场景。
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")

def test_camera_views():
    """测试不同相机视角"""
    print("=" * 60)
    print("SimplerEnv相机视角修复验证")
    print("=" * 60)
    
    try:
        # 导入环境
        from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
        
        print("✓ 成功导入环境模块")
        
        # 测试场景配置
        test_scenarios = [
            {
                "name": "相机测试_2物体",
                "target": "coke_can",
                "distractors": ["green_cube_3cm"],
                "description": "可乐罐 + 绿色立方体"
            },
            {
                "name": "相机测试_5物体",
                "target": "orange",
                "distractors": ["pepsi_can", "yellow_cube_3cm", "sponge", "bridge_spoon_generated_modified"],
                "description": "橙子 + 4个干扰物体"
            }
        ]
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n{'='*50}")
            print(f"场景 {i+1}: {scenario['name']}")
            print(f"描述: {scenario['description']}")
            print(f"{'='*50}")
            
            try:
                # 创建测试环境 - 使用google_robot_static确保有overhead_camera
                class CameraTestEnv(GraspSingleCustomInSceneEnv):
                    def __init__(self, target_id, distractor_ids, **kwargs):
                        kwargs.setdefault("model_ids", [target_id])
                        kwargs.setdefault("distractor_model_ids", distractor_ids)
                        kwargs.setdefault("robot", "google_robot_static")  # 确保使用google robot
                        super().__init__(**kwargs)
                    
                    def reset(self, seed=None, options=None):
                        if options is None:
                            options = dict()
                        options = options.copy()
                        options["distractor_model_ids"] = self.distractor_model_ids
                        return super().reset(seed=seed, options=options)
                
                # 创建环境
                env = CameraTestEnv(
                    target_id=scenario["target"],
                    distractor_ids=scenario["distractors"],
                    obs_mode="image",  # 使用image模式获取相机图像
                    control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",
                    render_mode="rgb_array"
                )
                
                print(f"✓ 成功创建环境")
                
                # 重置环境
                obs, info = env.reset()
                print(f"✓ 成功重置环境")
                
                # 统计物体
                target_count = 1
                distractor_count = len(getattr(env.unwrapped, 'distractor_objs', []))
                total_objects = target_count + distractor_count
                
                print(f"物体统计:")
                print(f"  - 目标物体: {target_count} ({scenario['target']})")
                print(f"  - 干扰物体: {distractor_count}")
                print(f"  - 总物体数: {total_objects}")
                
                # 获取场景中的所有actors
                actors = env.unwrapped._scene.get_all_actors()
                object_actors = [a for a in actors if a.name != 'arena']
                print(f"  - 场景物体: {len(object_actors)}")
                
                # 列出所有物体位置
                print(f"场景中的物体:")
                for j, actor in enumerate(object_actors):
                    pos = actor.pose.p
                    print(f"  {j+1}. {actor.name}: 位置({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f})")
                
                # 保存不同相机视角的图像
                if 'image' in obs:
                    print(f"可用相机:")
                    for camera_name in obs['image'].keys():
                        print(f"  - {camera_name}")
                        camera_data = obs['image'][camera_name]

                        # 尝试不同的图像键
                        rgb_img = None
                        for img_key in ['Color', 'rgb', 'color']:
                            if img_key in camera_data:
                                rgb_img = camera_data[img_key]
                                print(f"    找到图像数据: {img_key}")
                                break

                        if rgb_img is not None:
                            # 确保图像格式正确
                            if rgb_img.dtype != np.uint8:
                                rgb_img = (rgb_img * 255).astype(np.uint8)

                            output_path = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/camera_fix_{scenario['name']}_{camera_name}.png"
                            cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                            print(f"  - 保存{camera_name}图像: {output_path}")
                        else:
                            print(f"  - {camera_name}没有找到图像数据")
                            print(f"    可用键: {list(camera_data.keys())}")
                else:
                    print(f"  - 观察中没有图像数据")
                    # 尝试使用render方法
                    try:
                        rgb_img = env.render()
                        if rgb_img is not None:
                            output_path = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/camera_fix_{scenario['name']}_render.png"
                            cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                            print(f"  - 保存render图像: {output_path}")
                    except Exception as e:
                        print(f"  - render失败: {e}")
                
                env.close()
                print(f"✓ {scenario['name']} 验证完成")
                
            except Exception as e:
                print(f"✗ {scenario['name']} 验证失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 检查生成的图像
        print(f"\n{'='*60}")
        print("生成的相机修复图像:")
        print(f"{'='*60}")
        
        image_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
        image_files = list(image_dir.glob("camera_fix_*.png"))
        
        if image_files:
            for img_file in sorted(image_files):
                print(f"  - {img_file.name}")
            print(f"\n🎉 成功生成 {len(image_files)} 张相机修复图像！")
            print(f"📸 请检查这些图像确认相机视角是否正确显示物体。")
        else:
            print(f"❌ 没有生成图像文件")
        
        return len(image_files) > 0
        
    except Exception as e:
        print(f"\n✗ 相机修复验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("SimplerEnv相机视角修复验证")
    success = test_camera_views()
    
    if success:
        print(f"\n🎉 相机修复验证完成！")
        print(f"\n📋 请检查生成的图像:")
        print(f"   - 如果看到从上方俯视桌面的视角，说明overhead_camera工作正常")
        print(f"   - 如果看到侧面或其他角度，可能需要进一步调整相机配置")
    else:
        print(f"\n❌ 相机修复验证失败，需要进一步调试。")
