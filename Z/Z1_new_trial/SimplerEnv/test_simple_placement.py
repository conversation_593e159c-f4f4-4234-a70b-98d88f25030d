#!/usr/bin/env python3
"""
简化的物体放置测试

专门测试物体放置算法的稳定性
"""

import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))

from simpler_env_interface import create_scene_simple

def test_minimal_scene():
    """测试最简单的场景 - 只有2个物体"""
    print("🧪 测试最简单场景 - 2个物体")
    print("=" * 50)
    
    try:
        success, results, images = create_scene_simple(
            preset="desktop_picking",
            objects=["apple", "orange"],  # 只测试2个物体
            num_episodes=1,
            verbose=True
        )
        
        print(f"\n结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if results and 'episodes' in results:
            for episode in results['episodes']:
                if 'object_positions' in episode:
                    print("物体最终位置:")
                    for obj_info in episode['object_positions']:
                        name = obj_info['name']
                        pos = obj_info['position']
                        on_table = obj_info['on_table']
                        status = "✅ 桌面上" if on_table else "❌ 掉落"
                        print(f"  {name}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f}) {status}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_single_object():
    """测试单个物体"""
    print("\n🧪 测试单个物体")
    print("=" * 50)
    
    try:
        success, results, images = create_scene_simple(
            preset="laboratory_scene",  # 实验室场景，最多3个物体
            objects=["apple"],  # 只有1个物体
            num_episodes=1,
            verbose=True
        )
        
        print(f"\n结果: {'✅ 成功' if success else '❌ 失败'}")
        return success
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔬 SimplerEnv物体放置稳定性测试")
    print("=" * 60)
    
    tests = [
        ("单个物体", test_single_object),
        ("两个物体", test_minimal_scene),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始测试: {test_name}")
        try:
            success = test_func()
            results[test_name] = success
            print(f"✅ {test_name} 测试完成: {'成功' if success else '失败'}")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed_tests}/{total_tests} 测试通过")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests > 0:
        print("🎉 至少有部分测试通过！")
        print("💡 建议:")
        print("   - 如果单个物体成功，说明基础算法正确")
        print("   - 如果多个物体失败，可能是物体间碰撞问题")
        print("   - 可以尝试增大物体间距离")
    else:
        print("⚠️ 所有测试失败")
        print("💡 可能的问题:")
        print("   - 桌面高度设置不正确")
        print("   - 物理参数需要调整")
        print("   - 工作空间边界有问题")

if __name__ == "__main__":
    main()
