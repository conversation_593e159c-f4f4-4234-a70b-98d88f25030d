#!/usr/bin/env python3
"""
最终验证：修复物体掉落问题的多物体场景图像生成

使用稳定的物体放置策略，确保所有物体都正确显示在桌面上。
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")

def generate_fixed_multi_object_scenes():
    """生成修复后的多物体场景图像"""
    print("=" * 60)
    print("SimplerEnv修复物体掉落问题的最终验证")
    print("=" * 60)
    
    try:
        from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
        import sapien.core as sapien
        from transforms3d.euler import euler2quat
        
        print("✓ 成功导入环境模块")
        
        # 测试场景配置
        test_scenarios = [
            {
                "name": "修复简单2物体",
                "target": "coke_can",
                "distractors": ["green_cube_3cm"],
                "description": "可乐罐 + 绿色立方体 (修复版)"
            },
            {
                "name": "修复多样3物体", 
                "target": "apple",
                "distractors": ["coke_can", "blue_plastic_bottle"],
                "description": "苹果 + 可乐罐 + 蓝色瓶子 (修复版)"
            },
            {
                "name": "修复丰富5物体",
                "target": "orange",
                "distractors": ["pepsi_can", "yellow_cube_3cm", "sponge", "bridge_spoon_generated_modified"],
                "description": "橙子 + 百事罐 + 黄色立方体 + 海绵 + 勺子 (修复版)"
            },
            {
                "name": "修复复杂8物体",
                "target": "eggplant",
                "distractors": [
                    "opened_coke_can", "opened_pepsi_can", "green_cube_3cm", 
                    "yellow_cube_3cm", "apple", "orange", "bridge_carrot_generated_modified"
                ],
                "description": "茄子 + 7个干扰物体 (修复版)"
            }
        ]
        
        results = []
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n{'='*50}")
            print(f"场景 {i+1}: {scenario['name']}")
            print(f"描述: {scenario['description']}")
            print(f"{'='*50}")
            
            try:
                # 创建修复的测试环境
                class FixedPlacementEnv(GraspSingleCustomInSceneEnv):
                    def __init__(self, target_id, distractor_ids, **kwargs):
                        kwargs.setdefault("model_ids", [target_id])
                        kwargs.setdefault("distractor_model_ids", distractor_ids)
                        kwargs.setdefault("robot", "google_robot_static")
                        super().__init__(**kwargs)
                    
                    def reset(self, seed=None, options=None):
                        if options is None:
                            options = dict()
                        options = options.copy()
                        options["distractor_model_ids"] = self.distractor_model_ids
                        return super().reset(seed=seed, options=options)
                    
                    def _initialize_actors(self):
                        """修复的物体初始化方法 - 确保所有物体都不掉落"""
                        # 收集所有物体
                        all_objects = [self.obj]
                        if hasattr(self, 'distractor_objs') and self.distractor_objs:
                            all_objects.extend(self.distractor_objs)
                        
                        print(f"开始修复初始化 {len(all_objects)} 个物体")
                        
                        # 预定义安全位置
                        safe_positions = [
                            (-0.30, 0.10),  (-0.25, 0.10),  (-0.20, 0.10),
                            (-0.30, 0.20),  (-0.25, 0.20),  (-0.20, 0.20),
                            (-0.30, 0.30),  (-0.25, 0.30),  (-0.20, 0.30),
                            (-0.35, 0.15),  (-0.15, 0.15),  (-0.35, 0.25),
                        ]
                        
                        # 将机器人移到远处避免碰撞
                        self.agent.robot.set_pose(sapien.Pose([-10, 0, 0]))
                        
                        # 逐个放置物体
                        for i, obj in enumerate(all_objects):
                            if obj is None:
                                continue
                            
                            # 使用安全位置
                            if i < len(safe_positions):
                                x, y = safe_positions[i]
                            else:
                                x = self._episode_rng.uniform(-0.30, -0.20)
                                y = self._episode_rng.uniform(0.10, 0.30)
                            
                            # 直接放在桌面上
                            z = self.scene_table_height + 0.02
                            q = euler2quat(0, 0, 0)  # 无旋转
                            
                            print(f"修复放置物体 {obj.name} 在位置: ({x:.2f}, {y:.2f}, {z:.2f})")
                            obj.set_pose(sapien.Pose([x, y, z], q))
                            obj.set_damping(0.5, 0.5)
                            
                            # 每放置一个物体就稳定一下
                            self._settle(0.3)
                            
                            # 检查稳定性
                            pos_after = obj.pose.p
                            if pos_after[2] < self.scene_table_height - 0.05:
                                print(f"  重新调整 {obj.name}")
                                obj.set_pose(sapien.Pose([x, y, self.scene_table_height + 0.05], q))
                                obj.set_velocity(np.zeros(3))
                                obj.set_angular_velocity(np.zeros(3))
                                self._settle(0.5)
                        
                        # 最终稳定
                        self._settle(1.0)
                        
                        # 检查并修正掉落的物体
                        for i, obj in enumerate(all_objects):
                            if obj is not None:
                                pos = obj.pose.p
                                if pos[2] < self.scene_table_height - 0.05:
                                    print(f"  修正掉落的物体: {obj.name}")
                                    if i < len(safe_positions):
                                        x, y = safe_positions[i]
                                    else:
                                        x, y = -0.25, 0.20
                                    obj.set_pose(sapien.Pose([x, y, self.scene_table_height + 0.05], euler2quat(0, 0, 0)))
                                    obj.set_velocity(np.zeros(3))
                                    obj.set_angular_velocity(np.zeros(3))
                        
                        # 最终稳定
                        self._settle(0.5)
                        
                        # 记录目标物体高度
                        self.obj_height_after_settle = self.obj.pose.p[2]
                        print("修复物体初始化完成")
                
                # 创建环境
                env = FixedPlacementEnv(
                    target_id=scenario["target"],
                    distractor_ids=scenario["distractors"],
                    obs_mode="image",
                    control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",
                    render_mode="rgb_array"
                )
                
                print(f"✓ 成功创建环境")
                
                # 重置环境
                obs, info = env.reset()
                print(f"✓ 成功重置环境")
                
                # 统计物体
                target_count = 1
                distractor_count = len(getattr(env.unwrapped, 'distractor_objs', []))
                total_objects = target_count + distractor_count
                
                print(f"物体统计:")
                print(f"  - 目标物体: {target_count} ({scenario['target']})")
                print(f"  - 干扰物体: {distractor_count}")
                print(f"  - 总物体数: {total_objects}")
                
                # 获取场景中的所有actors
                actors = env.unwrapped._scene.get_all_actors()
                object_actors = [a for a in actors if a.name != 'arena']
                print(f"  - 场景物体: {len(object_actors)}")
                
                # 检查物体位置
                table_height = env.unwrapped.scene_table_height
                on_table_count = 0
                below_table_count = 0
                
                print(f"物体位置检查 (桌面高度: {table_height:.2f}):")
                for j, actor in enumerate(object_actors):
                    pos = actor.pose.p
                    if pos[2] >= table_height - 0.05:
                        on_table_count += 1
                        status = "✓ 在桌面上"
                    else:
                        below_table_count += 1
                        status = "✗ 掉落了"
                    print(f"  {j+1}. {actor.name}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f}) {status}")
                
                print(f"位置统计: 桌面上={on_table_count}, 掉落={below_table_count}")
                
                # 保存相机图像
                if 'image' in obs:
                    for camera_name in obs['image'].keys():
                        camera_data = obs['image'][camera_name]
                        
                        # 获取图像数据
                        rgb_img = None
                        for img_key in ['Color', 'rgb', 'color']:
                            if img_key in camera_data:
                                rgb_img = camera_data[img_key]
                                break
                        
                        if rgb_img is not None:
                            # 确保图像格式正确
                            if rgb_img.dtype != np.uint8:
                                rgb_img = (rgb_img * 255).astype(np.uint8)
                            
                            output_path = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/fixed_verification_{scenario['name']}_{camera_name}.png"
                            cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                            print(f"  - 保存{camera_name}图像: {output_path}")
                
                # 记录结果
                results.append({
                    "scenario": scenario['name'],
                    "description": scenario['description'],
                    "expected_objects": len(scenario['distractors']) + 1,
                    "actual_objects": total_objects,
                    "on_table": on_table_count,
                    "below_table": below_table_count,
                    "success": below_table_count == 0
                })
                
                env.close()
                print(f"✓ {scenario['name']} 验证完成")
                
            except Exception as e:
                print(f"✗ {scenario['name']} 验证失败: {e}")
                results.append({
                    "scenario": scenario['name'],
                    "description": scenario['description'],
                    "expected_objects": len(scenario['distractors']) + 1,
                    "actual_objects": 0,
                    "on_table": 0,
                    "below_table": 0,
                    "success": False,
                    "error": str(e)
                })
        
        # 总结结果
        print(f"\n{'='*60}")
        print("修复验证总结")
        print(f"{'='*60}")
        
        total_tests = len(results)
        success_count = sum(1 for r in results if r['success'])
        
        print(f"总场景数: {total_tests}")
        print(f"成功场景: {success_count}")
        print(f"成功率: {success_count/total_tests*100:.1f}%")
        
        print(f"\n详细结果:")
        for result in results:
            status = "✓" if result['success'] else "✗"
            print(f"  {status} {result['scenario']}: {result['description']}")
            if result['success']:
                print(f"    桌面上={result['on_table']}, 掉落={result['below_table']}")
            else:
                print(f"    失败原因: {result.get('error', '未知')}")
        
        return success_count == total_tests
        
    except Exception as e:
        print(f"\n✗ 修复验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("SimplerEnv修复物体掉落问题的最终验证")
    success = generate_fixed_multi_object_scenes()
    
    if success:
        print(f"\n🎉 所有场景修复验证通过！物体掉落问题已完全解决！")
        print(f"\n📸 修复验证图像已保存到: /home/<USER>/claude/SpatialVLA/Z/Z_new_trial/")
        print(f"   请查看 fixed_verification_*.png 文件确认所有物体都正确显示在桌面上。")
    else:
        print(f"\n⚠ 部分场景仍有问题，需要进一步调试。")
