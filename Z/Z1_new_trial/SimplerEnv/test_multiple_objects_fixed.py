#!/usr/bin/env python3
"""
测试修复后的多物体场景

验证不同数量的物体是否能正确显示在场景中。
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")

def test_multiple_objects():
    """测试不同数量的物体"""
    print("=" * 60)
    print("测试修复后的多物体场景")
    print("=" * 60)
    
    try:
        # 导入环境
        import mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env
        import gymnasium as gym
        from mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env import ObjectConfig, CustomSceneConfig
        
        print("✓ 成功导入环境模块")
        
        # 测试配置
        test_configs = [
            {
                "name": "2物体测试",
                "target_objects": [
                    ObjectConfig("can", "coke_can"),
                    ObjectConfig("cube", "green_cube_3cm")
                ],
                "distractor_objects": []
            },
            {
                "name": "3物体测试", 
                "target_objects": [
                    ObjectConfig("can", "coke_can"),
                    ObjectConfig("cube", "green_cube_3cm")
                ],
                "distractor_objects": [
                    ObjectConfig("fruit", "apple")
                ]
            },
            {
                "name": "5物体测试",
                "target_objects": [
                    ObjectConfig("can", "coke_can"),
                    ObjectConfig("cube", "green_cube_3cm"),
                    ObjectConfig("cube", "yellow_cube_3cm")
                ],
                "distractor_objects": [
                    ObjectConfig("fruit", "apple"),
                    ObjectConfig("bottle", "blue_plastic_bottle")
                ]
            },
            {
                "name": "8物体测试",
                "target_objects": [
                    ObjectConfig("can", "coke_can"),
                    ObjectConfig("can", "opened_pepsi_can"),
                    ObjectConfig("cube", "green_cube_3cm"),
                    ObjectConfig("cube", "yellow_cube_3cm")
                ],
                "distractor_objects": [
                    ObjectConfig("fruit", "apple"),
                    ObjectConfig("fruit", "orange"),
                    ObjectConfig("bottle", "blue_plastic_bottle"),
                    ObjectConfig("tool", "bridge_spoon_generated_modified")
                ]
            }
        ]
        
        results = []
        
        for i, config in enumerate(test_configs):
            print(f"\n{'='*40}")
            print(f"测试 {i+1}: {config['name']}")
            print(f"{'='*40}")
            
            try:
                # 使用官方的多物体环境模式
                from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv

                # 创建基于官方实现的测试环境
                class CustomTestEnv(GraspSingleCustomInSceneEnv):
                    def __init__(self, test_config, **kwargs):
                        self.test_config = test_config

                        # 设置目标物体（第一个物体作为目标）
                        target_model_id = test_config['target_objects'][0].model_id if test_config['target_objects'] else "coke_can"
                        kwargs.setdefault("model_ids", [target_model_id])

                        # 设置干扰物体ID列表
                        distractor_ids = []
                        # 添加剩余的目标物体作为干扰物体
                        for obj in test_config['target_objects'][1:]:
                            distractor_ids.append(obj.model_id)
                        # 添加干扰物体
                        for obj in test_config['distractor_objects']:
                            distractor_ids.append(obj.model_id)

                        if distractor_ids:
                            kwargs.setdefault("distractor_model_ids", distractor_ids)

                        super().__init__(**kwargs)

                    def reset(self, seed=None, options=None):
                        if options is None:
                            options = dict()
                        options = options.copy()

                        # 设置干扰物体ID（官方方式）
                        distractor_ids = []
                        for obj in self.test_config['target_objects'][1:]:
                            distractor_ids.append(obj.model_id)
                        for obj in self.test_config['distractor_objects']:
                            distractor_ids.append(obj.model_id)

                        if distractor_ids:
                            options["distractor_model_ids"] = distractor_ids

                        return super().reset(seed=seed, options=options)
                
                # 创建环境
                env = CustomTestEnv(
                    test_config=config,
                    obs_mode="state_dict",
                    control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",
                    render_mode="rgb_array"
                )
                
                print(f"✓ 成功创建环境")
                
                # 重置环境
                obs, info = env.reset()
                print(f"✓ 成功重置环境")
                
                # 检查场景信息（官方环境的信息结构不同）
                target_count = 1  # 官方环境总是有1个目标物体
                distractor_count = len(getattr(env.unwrapped, 'distractor_objs', []))
                total_objects = target_count + distractor_count
                
                print(f"场景信息:")
                print(f"  - 目标物体: {target_count}")
                print(f"  - 干扰物体: {distractor_count}")
                print(f"  - 总物体数: {total_objects}")
                
                # 获取actor信息
                actors = env.unwrapped._scene.get_all_actors()
                object_actors = [a for a in actors if a.name != 'arena']
                print(f"  - 实际场景物体: {len(object_actors)}")
                
                # 保存图像
                if 'image' in obs and 'overhead_camera' in obs['image']:
                    rgb_img = (obs['image']['overhead_camera']['rgb'] * 255).astype(np.uint8)
                    output_path = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/test_fixed_multiple_{config['name'].replace('物体', 'objects')}.png"
                    cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                    print(f"  - 保存图像: {output_path}")
                
                # 记录结果
                results.append({
                    "test_name": config['name'],
                    "expected_objects": len(config['target_objects']) + len(config['distractor_objects']),
                    "actual_objects": total_objects,
                    "scene_objects": len(object_actors),
                    "success": total_objects > 0 and len(object_actors) > 0
                })
                
                env.close()
                print(f"✓ {config['name']} 测试完成")
                
            except Exception as e:
                print(f"✗ {config['name']} 测试失败: {e}")
                results.append({
                    "test_name": config['name'],
                    "expected_objects": len(config['target_objects']) + len(config['distractor_objects']),
                    "actual_objects": 0,
                    "scene_objects": 0,
                    "success": False,
                    "error": str(e)
                })
        
        # 总结结果
        print(f"\n{'='*60}")
        print("测试总结")
        print(f"{'='*60}")
        
        success_count = sum(1 for r in results if r['success'])
        total_tests = len(results)
        
        print(f"总测试数: {total_tests}")
        print(f"成功测试: {success_count}")
        print(f"成功率: {success_count/total_tests*100:.1f}%")
        
        print(f"\n详细结果:")
        for result in results:
            status = "✓" if result['success'] else "✗"
            print(f"  {status} {result['test_name']}: "
                  f"期望{result['expected_objects']}个物体, "
                  f"实际{result['actual_objects']}个物体, "
                  f"场景{result['scene_objects']}个物体")
            if not result['success'] and 'error' in result:
                print(f"    错误: {result['error']}")
        
        return success_count == total_tests
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("SimplerEnv多物体场景修复验证")
    success = test_multiple_objects()
    
    if success:
        print("\n🎉 所有多物体测试通过！修复成功！")
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
