#!/usr/bin/env python3
import sys
sys.path.append('/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv')

from demo_improved_interface import create_scene_with_custom_positions, generate_grid_positions, generate_circle_positions

# 你的自定义位置配置
objects = ["sprite_can", "orange", "coke_can", "green_cube_3cm"]
positions = [
    (-0.50, 0.20, 0),    # 中心
    (-0.30, 0.15, 0),    # 左下
    (-0.20, 0.25, 0),    # 右上
    (-0.25, 0.10, 0)     # 下方
]

# 运行
success, results, images = create_scene_with_custom_positions(
    objects=objects,
    positions=positions,
    robot_type="google_robot_static",
    camera_type="google_robot_static",
    lighting_mode="natural",
    output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial",
    image_prefix="my_experiment",
    num_episodes=1,
    verbose=True
)

print(f"\n生成的图像: {images}")