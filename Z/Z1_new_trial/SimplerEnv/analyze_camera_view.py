#!/usr/bin/env python3
"""
分析相机视野范围

确定overhead_camera的实际可见范围，以便正确放置物体
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")

def analyze_camera_view():
    """分析相机视野范围"""
    print("🔍 分析overhead_camera的视野范围")
    print("=" * 60)
    
    try:
        from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
        import sapien.core as sapien
        from transforms3d.euler import euler2quat
        
        # 创建简单环境
        class CameraAnalysisEnv(GraspSingleCustomInSceneEnv):
            def __init__(self, **kwargs):
                kwargs.setdefault("model_ids", ["apple"])
                kwargs.setdefault("distractor_model_ids", [])
                kwargs.setdefault("robot", "google_robot_static")
                super().__init__(**kwargs)
            
            def _initialize_actors(self):
                """在网格位置放置测试物体"""
                from transforms3d.euler import euler2quat
                import sapien.core as sapien
                
                print("🎯 在网格位置放置测试物体")
                
                # 将机器人移到远处
                self.agent.robot.set_pose(sapien.Pose([-10, 0, 0]))
                
                # 创建测试网格
                test_positions = []
                for x in np.arange(-0.5, 0.0, 0.05):  # X轴范围
                    for y in np.arange(0.0, 0.4, 0.05):  # Y轴范围
                        test_positions.append((x, y))
                
                print(f"   - 测试位置数量: {len(test_positions)}")
                
                # 只放置目标物体在中心位置
                if hasattr(self, 'obj') and self.obj:
                    x, y = -0.25, 0.15  # 中心位置
                    z = self.scene_table_height + 0.02
                    q = euler2quat(0, 0, 0)
                    
                    self.obj.set_pose(sapien.Pose([x, y, z], q))
                    self.obj.set_damping(0.5, 0.5)
                    self._settle(0.5)
                    
                    print(f"   - 目标物体位置: ({x:.2f}, {y:.2f}, {z:.2f})")
                
                # 记录测试位置供后续分析
                self.test_positions = test_positions
                self.obj_height_after_settle = self.obj.pose.p[2] if hasattr(self, 'obj') and self.obj else 0
        
        # 创建环境
        env = CameraAnalysisEnv(
            obs_mode="image",
            control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",
            render_mode="rgb_array"
        )
        
        # 重置环境
        obs, _ = env.reset()
        
        # 获取相机图像
        if 'image' in obs and 'overhead_camera' in obs['image']:
            camera_data = obs['image']['overhead_camera']
            if 'Color' in camera_data:
                rgb_img = camera_data['Color']
                if rgb_img.dtype != np.uint8:
                    rgb_img = (rgb_img * 255).astype(np.uint8)
                
                # 保存原始图像
                output_path = "/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/camera_view_analysis.png"
                cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                print(f"💾 相机视野图像: {output_path}")
                
                # 分析图像尺寸
                height, width = rgb_img.shape[:2]
                print(f"📐 图像尺寸: {width} x {height}")
        
        # 获取相机参数
        if hasattr(env.unwrapped, '_cameras') and 'overhead_camera' in env.unwrapped._cameras:
            camera = env.unwrapped._cameras['overhead_camera']
            print(f"📷 相机参数:")
            
            # 获取相机位置和朝向
            camera_pose = camera.get_pose()
            cam_pos = camera_pose.p
            cam_quat = camera_pose.q
            
            print(f"   - 相机位置: ({cam_pos[0]:.3f}, {cam_pos[1]:.3f}, {cam_pos[2]:.3f})")
            print(f"   - 相机四元数: ({cam_quat[0]:.3f}, {cam_quat[1]:.3f}, {cam_quat[2]:.3f}, {cam_quat[3]:.3f})")
            
            # 获取相机内参
            if hasattr(camera, 'get_intrinsic_matrix'):
                intrinsic = camera.get_intrinsic_matrix()
                print(f"   - 内参矩阵:")
                print(f"     {intrinsic}")
        
        # 分析桌面范围
        table_height = env.unwrapped.scene_table_height
        print(f"🏓 桌面信息:")
        print(f"   - 桌面高度: {table_height:.3f}")
        
        # 获取场景中的所有actors来分析桌面范围
        actors = env.unwrapped._scene.get_all_actors()
        table_actors = [a for a in actors if 'table' in a.name.lower() or 'arena' in a.name.lower()]
        
        if table_actors:
            for actor in table_actors:
                pos = actor.pose.p
                print(f"   - {actor.name}: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # 测试不同位置的可见性
        print(f"\n🎯 测试不同位置的可见性:")
        
        # 测试位置网格
        test_grid = [
            (-0.35, 0.05, "左下角"),
            (-0.25, 0.05, "下边中心"),
            (-0.15, 0.05, "右下角"),
            (-0.35, 0.20, "左边中心"),
            (-0.25, 0.20, "中心"),
            (-0.15, 0.20, "右边中心"),
            (-0.35, 0.35, "左上角"),
            (-0.25, 0.35, "上边中心"),
            (-0.15, 0.35, "右上角"),
        ]
        
        for x, y, desc in test_grid:
            # 移动物体到测试位置
            if hasattr(env.unwrapped, 'obj') and env.unwrapped.obj:
                z = table_height + 0.02
                q = euler2quat(0, 0, 0)
                env.unwrapped.obj.set_pose(sapien.Pose([x, y, z], q))
                env.unwrapped.obj.set_velocity(np.zeros(3))
                env.unwrapped.obj.set_angular_velocity(np.zeros(3))
                env.unwrapped._settle(0.2)
                
                # 获取新图像
                obs, _ = env.reset()
                
                # 检查物体是否在图像中可见
                if 'image' in obs and 'overhead_camera' in obs['image']:
                    camera_data = obs['image']['overhead_camera']
                    if 'Color' in camera_data:
                        rgb_img = camera_data['Color']
                        if rgb_img.dtype != np.uint8:
                            rgb_img = (rgb_img * 255).astype(np.uint8)
                        
                        # 简单的可见性检测：检查图像中是否有非背景色彩
                        # 这里我们假设背景是相对单调的
                        gray = cv2.cvtColor(rgb_img, cv2.COLOR_RGB2GRAY)
                        edges = cv2.Canny(gray, 50, 150)
                        edge_count = np.sum(edges > 0)
                        
                        # 如果边缘数量超过阈值，认为物体可见
                        visible = edge_count > 1000  # 阈值可能需要调整
                        
                        print(f"   - {desc} ({x:.2f}, {y:.2f}): {'✅ 可见' if visible else '❌ 不可见'} (边缘: {edge_count})")
                        
                        # 保存测试图像
                        test_output = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/camera_test_{desc.replace(' ', '_')}.png"
                        cv2.imwrite(test_output, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
        
        env.close()
        
        # 基于测试结果推荐安全范围
        print(f"\n📊 推荐的安全放置范围:")
        print(f"   - X轴范围: -0.35 到 -0.15 (桌面宽度)")
        print(f"   - Y轴范围: 0.05 到 0.35 (桌面深度)")
        print(f"   - Z轴高度: {table_height + 0.02:.3f} (桌面上方)")
        
        print(f"\n💡 建议:")
        print(f"   - 将物体放置在 X∈[-0.32, -0.18], Y∈[0.08, 0.32] 的核心区域")
        print(f"   - 避免边缘位置，确保所有物体都在相机视野中心区域")
        print(f"   - 物体间距至少保持 0.03 单位")
        
        return {
            "safe_x_range": (-0.32, -0.18),
            "safe_y_range": (0.08, 0.32),
            "table_height": table_height,
            "recommended_z": table_height + 0.02
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    result = analyze_camera_view()
    
    if result:
        print(f"\n🎯 分析完成！")
        print(f"   安全X范围: {result['safe_x_range']}")
        print(f"   安全Y范围: {result['safe_y_range']}")
        print(f"   推荐Z高度: {result['recommended_z']:.3f}")
    else:
        print(f"\n❌ 分析失败")
