#!/usr/bin/env python3
"""
SimplerEnv标准化接口

为新手用户提供简单易用的SimplerEnv多物体场景生成接口。
所有重要参数都通过main()函数暴露，用户只需修改参数即可定制环境。
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")

def setup_environment():
    """设置SimplerEnv环境"""
    try:
        import sapien.core as sapien
        sapien.render_config.rt_use_denoiser = False
        return True
    except ImportError as e:
        print(f"❌ 环境设置失败: {e}")
        return False

def create_stable_environment(target_object, distractor_objects, robot_type="google_robot_static",
                             lighting_config=None, object_positions=None):
    """创建稳定的多物体环境，支持光照和位置控制"""
    from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
    import sapien.core as sapien
    import numpy as np

    class StableMultiObjectEnv(GraspSingleCustomInSceneEnv):
        def __init__(self, target_id, distractor_ids, lighting_config=None, object_positions=None, **kwargs):
            kwargs.setdefault("model_ids", [target_id])
            kwargs.setdefault("distractor_model_ids", distractor_ids)
            kwargs.setdefault("robot", robot_type)

            # 保存光照和位置配置
            self.lighting_config = lighting_config or {}
            self.object_positions = object_positions or []

            super().__init__(**kwargs)

        def reset(self, seed=None, options=None):
            if options is None:
                options = dict()
            options = options.copy()
            options["distractor_model_ids"] = self.distractor_model_ids
            return super().reset(seed=seed, options=options)

        def _setup_lighting(self):
            """设置自定义光照"""
            if self.bg_name is not None:
                return

            # 如果没有自定义光照配置，使用默认设置
            if not self.lighting_config:
                super()._setup_lighting()
                return

            print(f"🔆 应用自定义光照配置")

            # 设置环境光
            ambient_light = self.lighting_config.get('ambient_light', [0.3, 0.3, 0.3])
            self._scene.set_ambient_light(ambient_light)
            print(f"   - 环境光: RGB({ambient_light[0]:.2f}, {ambient_light[1]:.2f}, {ambient_light[2]:.2f})")

            # 添加方向光
            directional_lights = self.lighting_config.get('directional_lights', [])
            if not directional_lights:
                # 默认方向光
                directional_lights = [{
                    'direction': [1, 1, -1],
                    'color': [1.0, 1.0, 1.0],
                    'shadow': self.lighting_config.get('enable_shadow', True)
                }]

            for i, light in enumerate(directional_lights):
                direction = light.get('direction', [1, 1, -1])
                color = light.get('color', [1.0, 1.0, 1.0])
                shadow = light.get('shadow', False)
                scale = light.get('scale', 5)
                shadow_map_size = light.get('shadow_map_size', 2048)

                try:
                    if shadow and i == 0:  # 只有第一个光源启用阴影
                        self._scene.add_directional_light(
                            direction, color, shadow=True, scale=scale, shadow_map_size=shadow_map_size
                        )
                        print(f"   - 方向光{i+1}: 方向{direction}, 颜色{color}, 阴影=是")
                    else:
                        self._scene.add_directional_light(direction, color, shadow=False)
                        print(f"   - 方向光{i+1}: 方向{direction}, 颜色{color}, 阴影=否")
                except Exception as e:
                    print(f"   ⚠️ 添加方向光{i+1}失败: {e}")
                    # 降级为无阴影光源
                    try:
                        self._scene.add_directional_light(direction, color, shadow=False)
                        print(f"   - 方向光{i+1}: 降级为无阴影")
                    except Exception as e2:
                        print(f"   ❌ 完全无法添加方向光{i+1}: {e2}")
        
        def _initialize_actors(self):
            """增强稳定的物体初始化方法，支持自定义位置"""
            from transforms3d.euler import euler2quat
            import sapien.core as sapien

            # 收集所有物体
            all_objects = [self.obj]
            if hasattr(self, 'distractor_objs') and self.distractor_objs:
                all_objects.extend(self.distractor_objs)

            print(f"🔧 初始化 {len(all_objects)} 个物体")

            # 使用自定义位置或默认安全位置
            if self.object_positions and len(self.object_positions) >= len(all_objects):
                print(f"   📍 使用自定义物体位置")
                positions = self.object_positions[:len(all_objects)]
            else:
                print(f"   📍 使用默认安全位置网格")
                # 基于实际测试的安全位置网格，确保在相机视野内
                positions = [
                    (-0.25, 0.15),  # 中心位置 - 最安全
                    (-0.23, 0.17),  # 右上
                    (-0.27, 0.17),  # 左上
                    (-0.23, 0.13),  # 右下
                    (-0.27, 0.13),  # 左下
                    (-0.25, 0.19),  # 上方
                    (-0.25, 0.11),  # 下方
                    (-0.29, 0.15),  # 左侧
                    (-0.21, 0.15),  # 右侧
                    (-0.24, 0.16),  # 微调位置1
                    (-0.26, 0.14),  # 微调位置2
                    (-0.22, 0.15),  # 微调位置3
                ]

            # 将机器人移到远处
            self.agent.robot.set_pose(sapien.Pose([-10, 0, 0]))

            # 逐个稳定放置物体
            for i, obj in enumerate(all_objects):
                if obj is None:
                    print(f"   ⚠️ 第{i+1}个物体为None，跳过")
                    continue

                # 使用安全位置
                if i < len(positions):
                    x, y = positions[i]
                else:
                    # 备用位置，仍在安全范围内
                    x = -0.25 + (i % 3 - 1) * 0.03
                    y = 0.15 + (i // 3 - 1) * 0.03

                # 稍微高一点放置，避免碰撞
                z = self.scene_table_height + 0.05
                q = euler2quat(0, 0, 0)

                print(f"   📍 放置 {obj.name} 在位置: ({x:.2f}, {y:.2f}, {z:.2f})")

                # 多步骤稳定放置
                # 第1步：清零速度
                obj.set_velocity(np.zeros(3))
                obj.set_angular_velocity(np.zeros(3))

                # 第2步：设置位置
                obj.set_pose(sapien.Pose([x, y, z], q))

                # 第3步：设置阻尼
                obj.set_damping(0.8, 0.8)  # 增加阻尼

                # 第4步：短暂稳定
                self._settle(0.2)

                # 第5步：检查并修正位置
                pos_after = obj.pose.p
                print(f"      初始位置: ({pos_after[0]:.2f}, {pos_after[1]:.2f}, {pos_after[2]:.2f})")

                # 如果物体移动太远，强制重置
                if (abs(pos_after[0] - x) > 0.1 or
                    abs(pos_after[1] - y) > 0.1 or
                    pos_after[2] < self.scene_table_height - 0.05):

                    print(f"      ⚠️ 位置偏移过大，强制重置")
                    obj.set_velocity(np.zeros(3))
                    obj.set_angular_velocity(np.zeros(3))
                    obj.set_pose(sapien.Pose([x, y, self.scene_table_height + 0.08], q))
                    self._settle(0.3)

                # 第6步：再次检查
                pos_final = obj.pose.p
                print(f"      最终位置: ({pos_final[0]:.2f}, {pos_final[1]:.2f}, {pos_final[2]:.2f})")

            # 全局稳定
            print(f"   🔄 全局稳定中...")
            self._settle(1.0)

            # 最终修正：确保所有物体都在桌面上且在视野内
            print(f"   🔍 最终位置检查和修正:")
            for i, obj in enumerate(all_objects):
                if obj is None:
                    continue

                pos = obj.pose.p
                needs_fix = False

                # 检查是否掉落
                if pos[2] < self.scene_table_height - 0.05:
                    print(f"      ❌ {obj.name} 掉落，需要修正")
                    needs_fix = True

                # 检查是否在视野外 - 使用更严格的范围
                if not (-0.30 <= pos[0] <= -0.20 and 0.10 <= pos[1] <= 0.20):
                    print(f"      ❌ {obj.name} 在视野外，需要修正")
                    needs_fix = True

                if needs_fix:
                    # 使用原始安全位置
                    if i < len(positions):
                        x, y = positions[i]
                    else:
                        x, y = -0.25, 0.15

                    print(f"      🔧 修正 {obj.name} 到位置: ({x:.2f}, {y:.2f})")
                    obj.set_velocity(np.zeros(3))
                    obj.set_angular_velocity(np.zeros(3))
                    obj.set_pose(sapien.Pose([x, y, self.scene_table_height + 0.08], euler2quat(0, 0, 0)))
                    self._settle(0.3)
                else:
                    print(f"      ✅ {obj.name} 位置正常")

            # 最终稳定
            self._settle(0.5)

            # 记录目标物体高度
            if hasattr(self, 'obj') and self.obj:
                self.obj_height_after_settle = self.obj.pose.p[2]

            print(f"🎯 物体初始化完成")
    
    return StableMultiObjectEnv

def main(
    # 🎯 物体配置
    target_object="apple",
    distractor_objects=["coke_can", "green_cube_3cm", "orange"],
    available_objects=None,  # 可选物体池，如果提供则随机选择
    num_objects_range=(3, 8),  # 物体数量范围 (最小, 最大)

    # 📍 物体位置配置
    object_positions=None,  # 自定义物体位置列表 [(x1,y1), (x2,y2), ...]
    position_mode="safe_grid",  # "safe_grid", "random", "custom"
    workspace_bounds=((-0.32, -0.18), (0.08, 0.32)),  # ((x_min, x_max), (y_min, y_max))
    min_object_spacing=0.03,  # 物体间最小距离

    # 🔆 光照配置
    lighting_config=None,  # 自定义光照配置字典
    lighting_mode="default",  # "default", "bright", "dim", "dramatic", "soft", "colored"
    ambient_light=(0.3, 0.3, 0.3),  # 环境光RGB
    directional_lights=None,  # 方向光配置列表
    enable_shadow=True,  # 是否启用阴影

    # 📷 相机配置
    camera_type="overhead_camera",  # overhead_camera(推荐) / base_camera
    obs_mode="image",

    # 🤖 机器人配置
    robot_type="google_robot_static",
    control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",

    # 📁 输出配置
    output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial",
    image_prefix="custom_scene",
    save_images=True,

    # 🔧 运行配置
    num_episodes=3,
    render_mode="rgb_array",

    # 🎛️ 高级配置
    enable_stable_placement=True,
    verbose=True
):
    """
    SimplerEnv多物体场景生成主函数
    
    🎯 物体配置参数:
    - target_object: 目标物体类型 (如: "apple", "orange", "coke_can")
    - distractor_objects: 干扰物体列表 (如: ["pepsi_can", "green_cube_3cm"])
    
    📷 相机配置参数:
    - camera_type: 相机类型
      * "overhead_camera": 顶部俯视相机(推荐，能看到所有物体)
      * "base_camera": 侧面相机(可能被遮挡)
    
    📁 输出配置参数:
    - output_dir: 图像保存目录
    - image_prefix: 图像文件名前缀
    - save_images: 是否保存图像
    
    🔧 运行配置参数:
    - num_episodes: 生成场景数量
    - enable_stable_placement: 是否启用稳定放置(防止物体掉落)
    
    返回:
    - success: 是否成功
    - results: 详细结果
    - image_paths: 生成的图像路径列表
    """

    # 🔧 处理物体配置
    final_target_object = target_object
    final_distractor_objects = distractor_objects.copy()

    # 如果提供了可选物体池，随机选择物体
    if available_objects:
        import random
        num_objects = random.randint(*num_objects_range)
        selected_objects = random.sample(available_objects, min(num_objects, len(available_objects)))

        if selected_objects:
            final_target_object = selected_objects[0]
            final_distractor_objects = selected_objects[1:]

        if verbose:
            print(f"🎲 从物体池随机选择: 目标={final_target_object}, 干扰物体={final_distractor_objects}")

    # 🔆 处理光照配置
    final_lighting_config = lighting_config or {}

    if not lighting_config:
        # 根据光照模式生成配置
        if lighting_mode == "bright":
            final_lighting_config = {
                'ambient_light': [0.5, 0.5, 0.5],
                'directional_lights': [
                    {'direction': [0, 0, -1], 'color': [3.0, 3.0, 3.0], 'shadow': enable_shadow},
                    {'direction': [-1, -0.5, -1], 'color': [1.0, 1.0, 1.0], 'shadow': False},
                    {'direction': [1, 1, -1], 'color': [1.0, 1.0, 1.0], 'shadow': False}
                ]
            }
        elif lighting_mode == "dim":
            final_lighting_config = {
                'ambient_light': [0.1, 0.1, 0.1],
                'directional_lights': [
                    {'direction': [1, 1, -1], 'color': [0.5, 0.5, 0.5], 'shadow': enable_shadow}
                ]
            }
        elif lighting_mode == "dramatic":
            final_lighting_config = {
                'ambient_light': [0.02, 0.02, 0.02],
                'directional_lights': [
                    {'direction': [1, 0, -1], 'color': [2.0, 2.0, 2.0], 'shadow': enable_shadow},
                    {'direction': [-1, 0, -0.5], 'color': [0.2, 0.2, 0.2], 'shadow': False}
                ]
            }
        elif lighting_mode == "soft":
            final_lighting_config = {
                'ambient_light': [0.4, 0.4, 0.4],
                'directional_lights': [
                    {'direction': [0, 0, -1], 'color': [0.6, 0.6, 0.6], 'shadow': False},
                    {'direction': [1, 0, -0.5], 'color': [0.4, 0.4, 0.4], 'shadow': False},
                    {'direction': [-1, 0, -0.5], 'color': [0.4, 0.4, 0.4], 'shadow': False}
                ]
            }
        elif lighting_mode == "colored":
            final_lighting_config = {
                'ambient_light': [0.1, 0.1, 0.1],
                'directional_lights': [
                    {'direction': [0, 0, -1], 'color': [1.5, 0.8, 0.8], 'shadow': enable_shadow},
                    {'direction': [1, 1, -1], 'color': [0.8, 0.8, 1.5], 'shadow': False}
                ]
            }
        else:  # default
            final_lighting_config = {
                'ambient_light': list(ambient_light),
                'directional_lights': directional_lights or [
                    {'direction': [1, 1, -1], 'color': [1.0, 1.0, 1.0], 'shadow': enable_shadow}
                ],
                'enable_shadow': enable_shadow
            }

    # 📍 处理物体位置配置
    final_object_positions = object_positions

    if not object_positions and position_mode == "random":
        # 生成随机位置
        import random
        num_positions = len(final_distractor_objects) + 1
        final_object_positions = []

        for _ in range(num_positions):
            x = random.uniform(*workspace_bounds[0])
            y = random.uniform(*workspace_bounds[1])
            final_object_positions.append((x, y))

        if verbose:
            print(f"🎲 生成随机位置: {final_object_positions}")

    if verbose:
        print("🚀 SimplerEnv多物体场景生成 - 增强版")
        print("=" * 50)
        print(f"📊 配置参数:")
        print(f"   🎯 目标物体: {final_target_object}")
        print(f"   🎯 干扰物体: {final_distractor_objects}")
        print(f"   📷 相机类型: {camera_type}")
        print(f"   🤖 机器人类型: {robot_type}")
        print(f"   📁 输出目录: {output_dir}")
        print(f"   🔧 场景数量: {num_episodes}")
        print(f"   🛡️ 稳定放置: {enable_stable_placement}")
        print(f"   🔆 光照模式: {lighting_mode}")
        if final_object_positions:
            print(f"   📍 自定义位置: {len(final_object_positions)} 个")
        print("=" * 50)
    
    try:
        # 环境设置检查
        if not setup_environment():
            return False, {"error": "环境设置失败"}, []
        
        # 创建环境类
        if enable_stable_placement:
            EnvClass = create_stable_environment(
                final_target_object,
                final_distractor_objects,
                robot_type,
                lighting_config=final_lighting_config,
                object_positions=final_object_positions
            )
        else:
            from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
            EnvClass = GraspSingleCustomInSceneEnv
        
        results = {
            "episodes": [],
            "image_paths": [],
            "total_objects_expected": len(final_distractor_objects) + 1,
            "success_count": 0,
            "failure_count": 0,
            "lighting_config": final_lighting_config,
            "object_positions": final_object_positions,
            "final_target_object": final_target_object,
            "final_distractor_objects": final_distractor_objects
        }
        
        # 运行多个场景
        for episode in range(num_episodes):
            if verbose:
                print(f"\n🎬 生成场景 {episode + 1}/{num_episodes}")
            
            try:
                # 创建环境
                env = EnvClass(
                    target_id=final_target_object,
                    distractor_ids=final_distractor_objects,
                    lighting_config=final_lighting_config,
                    object_positions=final_object_positions,
                    obs_mode=obs_mode,
                    control_mode=control_mode,
                    render_mode=render_mode
                )
                
                # 重置环境
                obs, _ = env.reset()
                
                # 统计物体
                target_count = 1
                distractor_count = len(getattr(env.unwrapped, 'distractor_objs', []))
                total_objects = target_count + distractor_count
                
                # 检查物体位置
                actors = env.unwrapped._scene.get_all_actors()
                object_actors = [a for a in actors if a.name != 'arena']
                table_height = env.unwrapped.scene_table_height
                
                on_table_count = 0
                below_table_count = 0
                object_positions = []
                
                for actor in object_actors:
                    pos = actor.pose.p
                    object_positions.append({
                        "name": actor.name,
                        "position": [float(pos[0]), float(pos[1]), float(pos[2])],
                        "on_table": pos[2] >= table_height - 0.05
                    })
                    
                    if pos[2] >= table_height - 0.05:
                        on_table_count += 1
                    else:
                        below_table_count += 1
                
                # 保存图像
                episode_images = []
                if save_images and 'image' in obs:
                    for cam_name in obs['image'].keys():
                        if cam_name == camera_type or camera_type == "all":
                            camera_data = obs['image'][cam_name]
                            
                            # 获取图像数据
                            rgb_img = None
                            for img_key in ['Color', 'rgb', 'color']:
                                if img_key in camera_data:
                                    rgb_img = camera_data[img_key]
                                    break
                            
                            if rgb_img is not None:
                                if rgb_img.dtype != np.uint8:
                                    rgb_img = (rgb_img * 255).astype(np.uint8)
                                
                                output_path = f"{output_dir}/{image_prefix}_episode{episode+1}_{cam_name}.png"
                                cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                                episode_images.append(output_path)
                                
                                if verbose:
                                    print(f"   💾 保存图像: {output_path}")
                
                # 记录结果
                episode_result = {
                    "episode": episode + 1,
                    "target_object": target_object,
                    "distractor_objects": distractor_objects,
                    "total_objects_expected": len(distractor_objects) + 1,
                    "total_objects_actual": total_objects,
                    "on_table_count": on_table_count,
                    "below_table_count": below_table_count,
                    "success": below_table_count == 0,
                    "object_positions": object_positions,
                    "images": episode_images
                }
                
                results["episodes"].append(episode_result)
                results["image_paths"].extend(episode_images)
                
                if episode_result["success"]:
                    results["success_count"] += 1
                    if verbose:
                        print(f"   ✅ 成功: 桌面上={on_table_count}, 掉落={below_table_count}")
                else:
                    results["failure_count"] += 1
                    if verbose:
                        print(f"   ⚠️ 部分成功: 桌面上={on_table_count}, 掉落={below_table_count}")
                
                env.close()
                
            except Exception as e:
                if verbose:
                    print(f"   ❌ 场景 {episode + 1} 失败: {e}")
                results["failure_count"] += 1
                results["episodes"].append({
                    "episode": episode + 1,
                    "success": False,
                    "error": str(e)
                })
        
        # 总结
        success_rate = results["success_count"] / num_episodes * 100
        overall_success = results["success_count"] == num_episodes
        
        if verbose:
            print(f"\n📊 生成总结:")
            print(f"   🎯 总场景数: {num_episodes}")
            print(f"   ✅ 成功场景: {results['success_count']}")
            print(f"   ❌ 失败场景: {results['failure_count']}")
            print(f"   📈 成功率: {success_rate:.1f}%")
            print(f"   🖼️ 生成图像: {len(results['image_paths'])} 张")
            
            if overall_success:
                print(f"\n🎉 所有场景生成成功！")
            else:
                print(f"\n⚠️ 部分场景存在问题，建议检查配置。")
        
        return overall_success, results, results["image_paths"]
        
    except Exception as e:
        if verbose:
            print(f"\n❌ 生成失败: {e}")
        return False, {"error": str(e)}, []


if __name__ == "__main__":
    # 🎯 基础使用示例
    print("🎯 基础使用示例:")
    success, results, images = main()

    print("\n" + "="*60)

    # 🎨 自定义配置示例
    print("🎨 自定义配置示例:")
    success, results, images = main(
        target_object="orange",
        distractor_objects=["pepsi_can", "yellow_cube_3cm", "sponge", "bridge_spoon_generated_modified"],
        camera_type="overhead_camera",
        image_prefix="custom_5objects",
        num_episodes=2,
        verbose=True
    )

    print("\n" + "="*60)

    # 🔆 光照控制示例
    print("🔆 光照控制示例:")
    success, results, images = main(
        target_object="apple",
        distractor_objects=["coke_can", "green_cube_3cm"],
        lighting_mode="dramatic",  # 戏剧性光照
        enable_shadow=True,
        image_prefix="dramatic_lighting",
        num_episodes=1,
        verbose=True
    )

    print("\n" + "="*60)

    # 🎲 随机物体选择示例
    print("🎲 随机物体选择示例:")
    available_objects_pool = [
        "apple", "orange", "coke_can", "pepsi_can", "sprite_can",
        "green_cube_3cm", "yellow_cube_3cm", "sponge",
        "blue_plastic_bottle", "bridge_spoon_generated_modified"
    ]

    success, results, images = main(
        available_objects=available_objects_pool,
        num_objects_range=(4, 6),  # 随机选择4-6个物体
        lighting_mode="soft",  # 柔和光照
        position_mode="random",  # 随机位置
        image_prefix="random_objects",
        num_episodes=1,
        verbose=True
    )

    print("\n" + "="*60)

    # 📍 自定义位置示例
    print("📍 自定义位置示例:")
    custom_positions = [
        (-0.25, 0.15),  # 中心
        (-0.22, 0.18),  # 右上
        (-0.28, 0.12),  # 左下
        (-0.25, 0.21)   # 上方
    ]

    success, results, images = main(
        target_object="apple",
        distractor_objects=["coke_can", "green_cube_3cm", "orange"],
        object_positions=custom_positions,
        lighting_mode="colored",  # 彩色光照
        image_prefix="custom_positions",
        num_episodes=1,
        verbose=True
    )

    if success:
        print(f"\n🎊 完成！生成了 {len(images)} 张图像")
        print(f"📁 图像保存在: /home/<USER>/claude/SpatialVLA/Z/Z_new_trial/")
        print(f"\n💡 新功能说明:")
        print(f"   🔆 光照控制: 支持 default, bright, dim, dramatic, soft, colored 模式")
        print(f"   🎲 随机物体: 可从物体池中随机选择指定数量的物体")
        print(f"   📍 位置控制: 支持自定义物体位置或随机生成")
        print(f"   🎯 物体种类: 支持更多物体类型和数量控制")
    else:
        print(f"\n💡 提示: 如果遇到问题，请检查物体名称是否正确，或尝试减少物体数量。")
