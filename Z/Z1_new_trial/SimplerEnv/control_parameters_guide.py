#!/usr/bin/env python3
"""
SimplerEnv改进接口 - 完整参数控制指南

展示所有可控制的参数及其使用方法
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from simpler_env_interface import (
    create_scene_simple,
    create_scene_custom,
    create_scene_with_custom_positions,
    generate_grid_positions,
    generate_circle_positions,
    SceneConfigManager,
    AVAILABLE_OBJECTS,
    LIGHTING_CONFIGS,
    PRESET_CONFIGS
)

def show_all_controllable_parameters():
    """展示所有可控制的参数"""
    
    print("🎛️ SimplerEnv改进接口 - 完整参数控制指南")
    print("=" * 60)
    
    # 1. 物体控制
    print("\n1️⃣ 物体控制")
    print("-" * 30)
    print("📦 可用物体种类:")
    for obj, info in AVAILABLE_OBJECTS.items():
        print(f"   • {obj:<25} - {info['category']:<8} | {info['size']:<6} | {info['difficulty']}")
    
    print(f"\n📊 物体数量控制:")
    print(f"   • 推荐数量: 1-4个物体（成功率最高）")
    print(f"   • 最大支持: 8个物体")
    print(f"   • 不同预设有不同的推荐数量限制")
    
    # 2. 位置控制
    print("\n2️⃣ 物体位置控制")
    print("-" * 30)
    print("📍 位置控制方式:")
    print("   • 自动安全位置: 使用预定义的安全网格")
    print("   • 自定义位置: 精确指定每个物体的(x,y,z)坐标")
    print("   • 网格布局: 自动生成规整的网格排列")
    print("   • 圆形布局: 自动生成圆形排列")
    print("   • 工作空间边界: 定义物体可放置的区域范围")
    
    # 3. 环境控制
    print("\n3️⃣ 环境控制")
    print("-" * 30)
    print("🏠 桌面环境:")
    print("   • 桌面类型: 目前使用标准桌面（高度0.87m）")
    print("   • 桌面材质: 标准木质桌面")
    print("   • 背景环境: 简洁的实验室/工业环境")
    
    print("\n🔆 光照控制:")
    for mode, config in LIGHTING_CONFIGS.items():
        print(f"   • {mode:<18} - {config['description']}")
    
    # 4. 机器人控制
    print("\n4️⃣ 机器人控制")
    print("-" * 30)
    print("🤖 机器人类型:")
    print("   • google_robot_static  - Google机器人静态版本（推荐）")
    print("   • google_robot_mobile  - Google机器人移动版本")
    print("   • widowx              - WidowX机械臂")
    
    print("\n📷 相机控制:")
    print("   • overhead_camera     - 顶部俯视相机（推荐）")
    print("   • base_camera         - 侧面相机")
    
    # 5. 预设配置
    print("\n5️⃣ 预设配置")
    print("-" * 30)
    print("🎯 可用预设:")
    for preset_type, config in PRESET_CONFIGS.items():
        print(f"   • {preset_type.value:<20} - {config.description}")
        print(f"     └─ 推荐物体: {config.recommended_objects[:3]}...")
        print(f"     └─ 最大物体数: {config.max_objects}")

def demo_object_control():
    """演示物体控制"""
    print("\n" + "="*60)
    print("🎯 演示：物体控制")
    print("="*60)
    
    # 控制物体种类和数量
    print("\n1. 指定具体物体:")
    objects = ["apple", "orange", "coke_can"]  # 指定3个具体物体
    print(f"   选择的物体: {objects}")
    
    # 使用预设推荐物体
    print("\n2. 使用预设推荐物体:")
    recommended = SceneConfigManager.recommend_objects("desktop_picking", 4)
    print(f"   桌面抓取预设推荐: {recommended}")
    
    # 从物体池随机选择
    print("\n3. 从物体池选择:")
    available_pool = ["apple", "orange", "coke_can", "pepsi_can", "green_cube_3cm"]
    print(f"   物体池: {available_pool}")
    print(f"   可以随机选择其中的2-4个")

def demo_position_control():
    """演示位置控制"""
    print("\n" + "="*60)
    print("📍 演示：位置控制")
    print("="*60)
    
    # 1. 自定义精确位置
    print("\n1. 精确指定位置:")
    custom_positions = [
        (-0.25, 0.15, 0),    # apple在中心
        (-0.22, 0.18, 0),    # orange在右上
        (-0.28, 0.12, 0)     # coke_can在左下
    ]
    for i, pos in enumerate(custom_positions):
        print(f"   物体{i+1}: x={pos[0]:.2f}, y={pos[1]:.2f}, z={pos[2]:.2f}")
    
    # 2. 网格布局
    print("\n2. 网格布局:")
    grid_pos = generate_grid_positions(4)
    for i, pos in enumerate(grid_pos):
        print(f"   位置{i+1}: x={pos[0]:.2f}, y={pos[1]:.2f}, z={pos[2]:.2f}")
    
    # 3. 圆形布局
    print("\n3. 圆形布局:")
    circle_pos = generate_circle_positions(3, center=(-0.25, 0.20), radius=0.06)
    for i, pos in enumerate(circle_pos):
        print(f"   位置{i+1}: x={pos[0]:.2f}, y={pos[1]:.2f}, z={pos[2]:.2f}")
    
    # 4. 工作空间边界
    print("\n4. 工作空间边界控制:")
    workspaces = {
        "紧凑型": ((-0.28, -0.22), (0.12, 0.18)),
        "标准型": ((-0.30, -0.20), (0.10, 0.20)),
        "扩展型": ((-0.35, -0.15), (0.05, 0.35))
    }
    for name, bounds in workspaces.items():
        print(f"   {name}: x范围{bounds[0]}, y范围{bounds[1]}")

def demo_environment_control():
    """演示环境控制"""
    print("\n" + "="*60)
    print("🏠 演示：环境控制")
    print("="*60)
    
    # 光照控制
    print("\n1. 光照控制:")
    lighting_examples = {
        "明亮实验室": "laboratory_precise",
        "自然光照": "natural", 
        "工业环境": "industrial",
        "室内明亮": "indoor_bright"
    }
    for desc, mode in lighting_examples.items():
        config = LIGHTING_CONFIGS[mode]
        print(f"   {desc}: {mode}")
        print(f"     └─ 环境光: RGB{config['ambient_light']}")
        print(f"     └─ 方向光数量: {len(config['directional_lights'])}")
    
    # 机器人和相机组合
    print("\n2. 机器人+相机组合:")
    combinations = [
        ("google_robot_static", "overhead_camera", "推荐组合，稳定性最好"),
        ("google_robot_static", "base_camera", "侧面视角，适合特殊需求"),
        ("widowx", "base_camera", "真实机器人环境模拟")
    ]
    for robot, camera, desc in combinations:
        print(f"   {robot} + {camera}: {desc}")

def show_code_locations():
    """显示参数在代码中的位置"""
    print("\n" + "="*60)
    print("📝 代码中的参数位置")
    print("="*60)
    
    locations = {
        "物体库定义": "AVAILABLE_OBJECTS (第126-145行)",
        "光照配置": "LIGHTING_CONFIGS (第148-195行)", 
        "预设配置": "PRESET_CONFIGS (第49-125行)",
        "简单接口": "create_scene_simple() (第650-712行)",
        "中级接口": "create_scene_custom() (第715-802行)",
        "自定义位置": "create_scene_with_custom_positions() (第610-700行)",
        "布局生成": "generate_grid_positions(), generate_circle_positions() (第750-820行)",
        "配置验证": "ConfigValidator类 (第198-250行)"
    }
    
    for feature, location in locations.items():
        print(f"   {feature:<15}: {location}")

def practical_examples():
    """实用示例"""
    print("\n" + "="*60)
    print("💡 实用示例")
    print("="*60)
    
    print("\n示例1: 简单场景（新手推荐）")
    print("```python")
    print("success, results, images = create_scene_simple(")
    print("    preset='desktop_picking',  # 选择预设")
    print("    num_episodes=1")
    print(")")
    print("```")
    
    print("\n示例2: 自定义物体和光照")
    print("```python")
    print("success, results, images = create_scene_custom(")
    print("    robot_type='google_robot_static',")
    print("    camera_type='overhead_camera',")
    print("    lighting_mode='laboratory',      # 选择光照")
    print("    objects=['apple', 'orange'],     # 选择物体")
    print("    num_episodes=1")
    print(")")
    print("```")
    
    print("\n示例3: 精确位置控制")
    print("```python")
    print("objects = ['apple', 'orange', 'coke_can']")
    print("positions = [")
    print("    (-0.25, 0.15, 0),  # 中心")
    print("    (-0.22, 0.18, 0),  # 右上")
    print("    (-0.28, 0.12, 0)   # 左下")
    print("]")
    print("success, results, images = create_scene_with_custom_positions(")
    print("    objects=objects,")
    print("    positions=positions,")
    print("    lighting_mode='indoor_bright',   # 选择光照")
    print("    num_episodes=1")
    print(")")
    print("```")
    
    print("\n示例4: 网格布局")
    print("```python")
    print("objects = ['apple', 'orange', 'coke_can', 'green_cube_3cm']")
    print("positions = generate_grid_positions(4)  # 自动生成4个位置")
    print("success, results, images = create_scene_with_custom_positions(")
    print("    objects=objects,")
    print("    positions=positions,")
    print("    num_episodes=1")
    print(")")
    print("```")

def main():
    """主函数"""
    show_all_controllable_parameters()
    demo_object_control()
    demo_position_control() 
    demo_environment_control()
    show_code_locations()
    practical_examples()
    
    print("\n" + "="*60)
    print("🎉 参数控制指南完成！")
    print("💡 您可以控制:")
    print("   • 物体种类和数量（10种物体，1-8个）")
    print("   • 物体位置（精确坐标、网格、圆形布局）")
    print("   • 光照环境（5种专业光照模式）")
    print("   • 机器人类型（3种机器人）")
    print("   • 相机视角（2种相机）")
    print("   • 工作空间边界")
    print("   • 预设配置（5种专业预设）")

if __name__ == "__main__":
    main()
