#!/usr/bin/env python3
"""
最终验证：生成多物体场景图像

验证修复后的SimplerEnv能够正确显示不同数量的物体，并生成验证图像。
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")

def generate_multi_object_scenes():
    """生成多物体场景图像"""
    print("=" * 60)
    print("SimplerEnv多物体场景最终验证")
    print("=" * 60)
    
    try:
        # 导入环境
        import mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env
        import gymnasium as gym
        from mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env import ObjectConfig, CustomSceneConfig
        from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
        
        print("✓ 成功导入环境模块")
        
        # 测试场景配置
        test_scenarios = [
            {
                "name": "简单2物体",
                "target": "coke_can",
                "distractors": ["green_cube_3cm"],
                "description": "可乐罐 + 绿色立方体"
            },
            {
                "name": "多样3物体", 
                "target": "apple",
                "distractors": ["coke_can", "blue_plastic_bottle"],
                "description": "苹果 + 可乐罐 + 蓝色瓶子"
            },
            {
                "name": "丰富5物体",
                "target": "orange",
                "distractors": ["pepsi_can", "yellow_cube_3cm", "sponge", "bridge_spoon_generated_modified"],
                "description": "橙子 + 百事罐 + 黄色立方体 + 海绵 + 勺子"
            },
            {
                "name": "复杂8物体",
                "target": "eggplant",
                "distractors": [
                    "opened_coke_can", "opened_pepsi_can", "green_cube_3cm", 
                    "yellow_cube_3cm", "apple", "orange", "bridge_carrot_generated_modified"
                ],
                "description": "茄子 + 6个干扰物体"
            }
        ]
        
        results = []
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n{'='*50}")
            print(f"场景 {i+1}: {scenario['name']}")
            print(f"描述: {scenario['description']}")
            print(f"{'='*50}")
            
            try:
                # 创建测试环境
                class ScenarioTestEnv(GraspSingleCustomInSceneEnv):
                    def __init__(self, target_id, distractor_ids, **kwargs):
                        kwargs.setdefault("model_ids", [target_id])
                        kwargs.setdefault("distractor_model_ids", distractor_ids)
                        # 确保使用正确的机器人配置以获得overhead_camera
                        kwargs.setdefault("robot", "google_robot_static")
                        super().__init__(**kwargs)

                    def reset(self, seed=None, options=None):
                        if options is None:
                            options = dict()
                        options = options.copy()
                        options["distractor_model_ids"] = self.distractor_model_ids
                        return super().reset(seed=seed, options=options)
                
                # 创建环境
                env = ScenarioTestEnv(
                    target_id=scenario["target"],
                    distractor_ids=scenario["distractors"],
                    obs_mode="image",  # 使用image模式获取相机图像
                    control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",
                    render_mode="rgb_array"
                )
                
                print(f"✓ 成功创建环境")
                
                # 重置环境
                obs, info = env.reset()
                print(f"✓ 成功重置环境")
                
                # 统计物体
                target_count = 1
                distractor_count = len(getattr(env.unwrapped, 'distractor_objs', []))
                total_objects = target_count + distractor_count
                
                print(f"物体统计:")
                print(f"  - 目标物体: {target_count} ({scenario['target']})")
                print(f"  - 干扰物体: {distractor_count}")
                print(f"  - 总物体数: {total_objects}")
                
                # 获取场景中的所有actors
                actors = env.unwrapped._scene.get_all_actors()
                object_actors = [a for a in actors if a.name != 'arena']
                print(f"  - 场景物体: {len(object_actors)}")
                
                # 列出所有物体
                print(f"场景中的物体:")
                for j, actor in enumerate(object_actors):
                    pos = actor.pose.p
                    print(f"  {j+1}. {actor.name}: 位置({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f})")
                
                # 保存相机图像
                if 'image' in obs:
                    print(f"可用相机:")
                    for camera_name in obs['image'].keys():
                        print(f"  - {camera_name}")
                        camera_data = obs['image'][camera_name]

                        # 获取图像数据
                        rgb_img = None
                        for img_key in ['Color', 'rgb', 'color']:
                            if img_key in camera_data:
                                rgb_img = camera_data[img_key]
                                break

                        if rgb_img is not None:
                            # 确保图像格式正确
                            if rgb_img.dtype != np.uint8:
                                rgb_img = (rgb_img * 255).astype(np.uint8)

                            output_path = f"/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/final_verification_{scenario['name']}_{camera_name}.png"
                            cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                            print(f"  - 保存{camera_name}图像: {output_path}")
                else:
                    print(f"  - 观察中没有图像数据")
                
                # 记录结果
                results.append({
                    "scenario": scenario['name'],
                    "description": scenario['description'],
                    "expected_objects": len(scenario['distractors']) + 1,
                    "actual_objects": total_objects,
                    "scene_objects": len(object_actors),
                    "success": total_objects == len(scenario['distractors']) + 1
                })
                
                env.close()
                print(f"✓ {scenario['name']} 验证完成")
                
            except Exception as e:
                print(f"✗ {scenario['name']} 验证失败: {e}")
                results.append({
                    "scenario": scenario['name'],
                    "description": scenario['description'],
                    "expected_objects": len(scenario['distractors']) + 1,
                    "actual_objects": 0,
                    "scene_objects": 0,
                    "success": False,
                    "error": str(e)
                })
        
        # 总结结果
        print(f"\n{'='*60}")
        print("最终验证总结")
        print(f"{'='*60}")
        
        success_count = sum(1 for r in results if r['success'])
        total_tests = len(results)
        
        print(f"总场景数: {total_tests}")
        print(f"成功场景: {success_count}")
        print(f"成功率: {success_count/total_tests*100:.1f}%")
        
        print(f"\n详细结果:")
        for result in results:
            status = "✓" if result['success'] else "✗"
            print(f"  {status} {result['scenario']}: {result['description']}")
            print(f"    期望{result['expected_objects']}个物体, 实际{result['actual_objects']}个物体")
            if not result['success'] and 'error' in result:
                print(f"    错误: {result['error']}")
        
        # 生成图像列表
        print(f"\n生成的验证图像:")
        image_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
        image_files = list(image_dir.glob("final_verification_*.png"))
        for img_file in sorted(image_files):
            print(f"  - {img_file.name}")
        
        return success_count == total_tests
        
    except Exception as e:
        print(f"\n✗ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("SimplerEnv多物体场景最终验证")
    success = generate_multi_object_scenes()
    
    if success:
        print(f"\n🎉 所有场景验证通过！SimplerEnv多物体修复完全成功！")
        print(f"\n📸 验证图像已保存到: /home/<USER>/claude/SpatialVLA/Z/Z_new_trial/")
        print(f"   可以查看这些图像确认物体正确显示在场景中。")
    else:
        print(f"\n❌ 部分场景验证失败，需要进一步调试。")
