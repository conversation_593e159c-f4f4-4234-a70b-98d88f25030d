#!/usr/bin/env python3
"""
测试blue_plastic_bottle的脚本

直接调用您修改过的main()函数
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

# 导入您修改过的main函数
from simpler_env_interface import main

def test_blue_bottle_main():
    """测试使用您修改的main()函数"""
    print("🧪 测试blue_plastic_bottle - 使用修改后的main()函数")
    print("=" * 60)
    
    # 直接调用main()函数，它会使用您设置的默认参数
    success, results, images = main(
        num_episodes=1,
        verbose=True
    )
    
    print(f"\n结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if results and 'episodes' in results:
        for episode in results['episodes']:
            if 'object_positions' in episode:
                print("物体最终位置:")
                for obj_info in episode['object_positions']:
                    name = obj_info['name']
                    pos = obj_info['position']
                    on_table = obj_info['on_table']
                    status = "✅ 桌面上" if on_table else "❌ 掉落"
                    print(f"  {name}: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f}) {status}")
    
    return success

def test_blue_bottle_custom():
    """测试使用自定义接口指定blue_plastic_bottle"""
    print("\n🧪 测试blue_plastic_bottle - 使用自定义接口")
    print("=" * 60)
    
    from simpler_env_interface import create_scene_custom
    
    # 使用自定义接口明确指定blue_plastic_bottle
    success, results, images = create_scene_custom(
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="laboratory",
        objects=["blue_plastic_bottle", "coke_can"],  # 明确指定物体
        num_episodes=1,
        verbose=True
    )
    
    print(f"\n结果: {'✅ 成功' if success else '❌ 失败'}")
    return success

def test_blue_bottle_positions():
    """测试blue_plastic_bottle的自定义位置"""
    print("\n🧪 测试blue_plastic_bottle - 自定义位置")
    print("=" * 60)
    
    from simpler_env_interface import create_scene_with_custom_positions
    
    # 为blue_plastic_bottle指定特殊位置（因为它比较大）
    objects = ["blue_plastic_bottle", "green_cube_3cm"]
    positions = [
        (-0.25, 0.15, 0),    # blue_plastic_bottle在中心
        (-0.20, 0.20, 0),    # green_cube_3cm在旁边，距离稍远
    ]
    
    success, results, images = create_scene_with_custom_positions(
        objects=objects,
        positions=positions,
        lighting_mode="indoor_bright",
        image_prefix="blue_bottle_test",
        num_episodes=1,
        verbose=True
    )
    
    print(f"\n结果: {'✅ 成功' if success else '❌ 失败'}")
    return success

def main_test():
    """主测试函数"""
    print("🔵 Blue Plastic Bottle 测试")
    print("=" * 60)
    print("注意：blue_plastic_bottle是'困难'级别的物体，可能需要特殊处理")
    print()
    
    tests = [
        ("修改后的main()函数", test_blue_bottle_main),
        ("自定义接口", test_blue_bottle_custom),
        ("自定义位置", test_blue_bottle_positions),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 开始测试: {test_name}")
            success = test_func()
            results[test_name] = success
            print(f"✅ {test_name} 测试完成: {'成功' if success else '失败'}")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed_tests}/{total_tests} 测试通过")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n💡 关于blue_plastic_bottle:")
    print("   • 这是'困难'级别的物体，比较大且不稳定")
    print("   • 建议与小物体搭配，避免碰撞")
    print("   • 如果失败，可以尝试自定义位置，增大物体间距")

if __name__ == "__main__":
    main_test()
