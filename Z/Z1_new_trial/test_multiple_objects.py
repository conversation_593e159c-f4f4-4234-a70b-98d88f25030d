#!/usr/bin/env python3
"""
测试SimplerEnv多物体生成功能
验证是否真正能够生成多个物体而不是仅仅替换物体类型
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path

# 添加路径
sys.path.insert(0, '/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv')
sys.path.insert(0, '/home/<USER>/claude/SpatialVLA/Z/Z1_new_trial/SimplerEnv/ManiSkill2_real2sim')

def setup_environment():
    """设置环境"""
    try:
        import sapien.core as sapien
        sapien.render_config.rt_use_denoiser = False
        
        from mani_skill2_real2sim.envs.custom_scenes.diverse_scene_env import DiverseEnhancedSceneEnv
        from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict
        
        print("✅ 环境设置成功")
        return DiverseEnhancedSceneEnv, get_image_from_maniskill2_obs_dict
        
    except Exception as e:
        print(f"❌ 环境设置失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_object_generation():
    """测试物体生成"""
    print("🧪 测试多物体生成功能")
    print("=" * 60)
    
    DiverseEnhancedSceneEnv, get_image_from_obs = setup_environment()
    if DiverseEnhancedSceneEnv is None:
        return False
    
    # 测试不同的物体数量配置
    test_configs = [
        {"name": "基础测试", "range": (2, 4), "expected_min": 2},
        {"name": "中等测试", "range": (5, 8), "expected_min": 5},
        {"name": "大量测试", "range": (10, 15), "expected_min": 10},
    ]
    
    output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/multiple_objects_test")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    all_results = {}
    
    for config in test_configs:
        print(f"\n🔍 {config['name']}: 期望 {config['range']} 个物体")
        print("-" * 40)
        
        try:
            env = DiverseEnhancedSceneEnv(
                robot="google_robot_static",
                scene_name="dummy_tabletop",
                obs_mode="image",
                camera_cfgs={"add_segmentation": True},
                render_mode="cameras",
                num_objects_range=config['range'],
            )
            
            results = {
                "config": config,
                "tests": [],
                "success": False
            }
            
            # 进行多次测试
            for test_id in range(5):
                print(f"  测试 {test_id + 1}/5...")
                
                obs, info = env.reset()
                
                # 获取配置信息
                scene_config = info.get("scene_config", {})
                configured_total = scene_config.get("total_objects", 0)
                configured_targets = scene_config.get("num_target_objects", 0)
                configured_distractors = scene_config.get("num_distractor_objects", 0)
                
                # 获取实际物体信息
                actual_total = 0
                actual_targets = 0
                actual_distractors = 0
                object_names = []
                
                if hasattr(env, 'all_scene_objects'):
                    actual_total = len(env.all_scene_objects)
                    for obj in env.all_scene_objects:
                        object_names.append(obj.name)
                
                if hasattr(env, 'target_objects'):
                    actual_targets = len(env.target_objects)
                    
                if hasattr(env, 'distractor_objects'):
                    actual_distractors = len(env.distractor_objects)
                
                # 尝试保存图像
                image_path = None
                try:
                    # 检查观察结构
                    print(f"    观察结构: {list(obs.keys()) if isinstance(obs, dict) else type(obs)}")
                    if isinstance(obs, dict) and "image" in obs:
                        print(f"    图像键: {list(obs['image'].keys())}")
                        # 尝试获取第一个相机的图像
                        camera_names = list(obs['image'].keys())
                        if camera_names:
                            camera_name = camera_names[0]
                            camera_obs = obs['image'][camera_name]
                            print(f"    相机 {camera_name} 观察: {list(camera_obs.keys())}")

                            if 'rgb' in camera_obs:
                                image = camera_obs['rgb']
                            elif 'Color' in camera_obs:
                                image = camera_obs['Color']
                            else:
                                print(f"    未找到RGB图像，跳过保存")
                                image = None

                            if image is not None:
                                image_path = output_dir / f"{config['name'].replace(' ', '_')}_test_{test_id + 1}.png"
                                cv2.imwrite(str(image_path), cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
                except Exception as e:
                    print(f"    图像保存失败: {e}")
                    image_path = None
                
                test_result = {
                    "test_id": test_id + 1,
                    "configured": {
                        "total": configured_total,
                        "targets": configured_targets,
                        "distractors": configured_distractors
                    },
                    "actual": {
                        "total": actual_total,
                        "targets": actual_targets,
                        "distractors": actual_distractors
                    },
                    "object_names": object_names,
                    "image_path": str(image_path)
                }
                
                results["tests"].append(test_result)
                
                # 打印详细信息
                print(f"    配置: 总计{configured_total} (目标{configured_targets} + 干扰{configured_distractors})")
                print(f"    实际: 总计{actual_total} (目标{actual_targets} + 干扰{actual_distractors})")
                print(f"    物体: {', '.join(object_names)}")
                print(f"    图像: {image_path}")
                
                # 检查是否达到最小期望
                if actual_total >= config['expected_min']:
                    results["success"] = True
            
            env.close()
            all_results[config['name']] = results
            
            # 统计结果
            actual_counts = [t["actual"]["total"] for t in results["tests"]]
            configured_counts = [t["configured"]["total"] for t in results["tests"]]
            
            print(f"\n  📊 {config['name']} 统计:")
            print(f"    配置范围: {min(configured_counts)} - {max(configured_counts)}")
            print(f"    实际范围: {min(actual_counts)} - {max(actual_counts)}")
            print(f"    平均实际: {np.mean(actual_counts):.1f}")
            print(f"    成功率: {'✅' if results['success'] else '❌'}")
            
        except Exception as e:
            print(f"    ❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            all_results[config['name']] = {"error": str(e)}
    
    # 总结报告
    print(f"\n📋 总结报告")
    print("=" * 60)
    
    success_count = 0
    total_tests = len(test_configs)
    
    for config_name, results in all_results.items():
        if "error" in results:
            print(f"❌ {config_name}: 测试失败 - {results['error']}")
        else:
            status = "✅ 成功" if results["success"] else "❌ 失败"
            actual_counts = [t["actual"]["total"] for t in results["tests"]]
            print(f"{status} {config_name}: 实际物体数量 {min(actual_counts)}-{max(actual_counts)}")
            if results["success"]:
                success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{total_tests} 个测试成功")
    print(f"📁 测试图像保存在: {output_dir}")
    
    return success_count == total_tests

if __name__ == "__main__":
    print("🚀 SimplerEnv 多物体生成测试")
    print("=" * 60)
    
    success = test_object_generation()
    
    if success:
        print("\n🎉 所有测试通过！多物体生成功能正常工作")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试多物体生成机制")
    
    print("\n" + "=" * 60)
