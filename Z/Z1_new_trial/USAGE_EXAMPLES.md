# 自定义多样化场景使用示例

本文档提供了详细的使用示例，展示如何利用新的自定义场景系统进行训练数据生成和模型训练。

## 📋 示例目录

- [基础使用示例](#基础使用示例)
- [训练数据生成](#训练数据生成)
- [模型训练集成](#模型训练集成)
- [多样性分析](#多样性分析)
- [批量处理](#批量处理)

## 🎯 基础使用示例

### 示例1: 创建和运行基础多样化环境

```python
#!/usr/bin/env python3
import sys
sys.path.append("SimplerEnv")
import simpler_env
import numpy as np

def basic_diverse_scene_example():
    """基础多样化场景示例"""
    
    # 创建环境
    env = simpler_env.make("custom_diverse_pick_scene")
    print("✓ 环境创建成功")
    
    # 运行多个episode
    for episode in range(5):
        print(f"\n--- Episode {episode + 1} ---")
        
        # 重置环境（每次都会生成不同的场景配置）
        obs, info = env.reset(seed=episode * 100)
        
        # 打印场景信息
        scene_config = info.get("scene_config", {})
        print(f"场景配置: {scene_config}")
        
        # 获取语言指令
        instruction = env.get_language_instruction()
        print(f"任务指令: {instruction}")
        
        # 运行几步
        for step in range(10):
            action = env.action_space.sample()
            obs, reward, terminated, truncated, info = env.step(action)
            
            if terminated or truncated:
                print(f"Episode结束于步骤 {step + 1}")
                break
    
    env.close()
    print("✓ 基础示例完成")

if __name__ == "__main__":
    basic_diverse_scene_example()
```

### 示例2: 使用不同多样性级别

```python
def diversity_level_comparison():
    """比较不同多样性级别的环境"""
    
    diversity_levels = [
        ("低多样性", "custom_enhanced_low_diversity"),
        ("中等多样性", "custom_enhanced_medium_diversity"),
        ("高多样性", "custom_enhanced_high_diversity"),
        ("极高多样性", "custom_enhanced_extreme_diversity")
    ]
    
    for level_name, env_name in diversity_levels:
        print(f"\n{'='*50}")
        print(f"测试 {level_name}: {env_name}")
        print(f"{'='*50}")
        
        env = simpler_env.make(env_name)
        
        # 重置并获取多样性信息
        obs, info = env.reset(seed=42)
        
        diversity_info = info.get("diversity_info", {})
        print(f"应用的多样性变化: {list(diversity_info.keys())}")
        
        if "lighting" in diversity_info:
            print(f"光照类型: {diversity_info['lighting'].get('type')}")
        
        if "materials" in diversity_info:
            material_count = len(diversity_info['materials'])
            print(f"材质变化物体数量: {material_count}")
        
        # 获取多样性摘要（如果支持）
        if hasattr(env, 'get_diversity_summary'):
            summary = env.get_diversity_summary()
            print(f"多样性摘要: {summary}")
        
        env.close()
```

## 🏭 训练数据生成

### 示例3: 批量生成训练数据

```python
import json
from pathlib import Path
import cv2
from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict

def generate_training_dataset(num_episodes=100, output_dir="training_data"):
    """生成训练数据集"""
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 使用专门的训练数据生成环境
    env = simpler_env.make("custom_training_data_generation")
    
    dataset = []
    
    for episode in range(num_episodes):
        print(f"生成Episode {episode + 1}/{num_episodes}")
        
        # 重置环境
        obs, info = env.reset(seed=episode)
        
        # 获取初始图像
        image = get_image_from_maniskill2_obs_dict(env, obs)
        
        # 获取语言指令
        instruction = env.get_language_instruction()
        
        # 获取多样性信息
        diversity_summary = env.get_diversity_summary() if hasattr(env, 'get_diversity_summary') else {}
        
        # 保存图像
        image_filename = f"episode_{episode:04d}_initial.png"
        image_path = output_path / image_filename
        cv2.imwrite(str(image_path), cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
        
        # 记录数据
        episode_data = {
            "episode_id": episode,
            "image_path": image_filename,
            "instruction": instruction,
            "scene_config": info.get("scene_config", {}),
            "diversity_info": info.get("diversity_info", {}),
            "diversity_summary": diversity_summary
        }
        
        dataset.append(episode_data)
    
    # 保存数据集元信息
    dataset_info = {
        "total_episodes": num_episodes,
        "environment": "custom_training_data_generation",
        "episodes": dataset
    }
    
    with open(output_path / "dataset.json", "w", encoding="utf-8") as f:
        json.dump(dataset_info, f, indent=2, ensure_ascii=False)
    
    env.close()
    print(f"✓ 训练数据集已保存到: {output_path}")
    return dataset_info
```

### 示例4: 生成特定配置的数据

```python
from custom_scene_config import SceneConfigGenerator, CustomSceneConfig, ObjectConfig

def generate_specific_config_data():
    """生成特定配置的训练数据"""
    
    # 创建配置生成器
    generator = SceneConfigGenerator()
    
    # 定义特定的配置要求
    config_templates = [
        {
            "name": "simple_pick",
            "num_objects_range": (2, 3),
            "include_distractors": True
        },
        {
            "name": "cluttered_pick", 
            "num_objects_range": (5, 8),
            "include_distractors": True
        },
        {
            "name": "minimal_pick",
            "num_objects_range": (1, 2),
            "include_distractors": False
        }
    ]
    
    all_configs = []
    
    for template in config_templates:
        print(f"生成配置: {template['name']}")
        
        # 为每个模板生成多个配置
        for i in range(20):
            config = generator.generate_random_config(
                num_objects_range=template["num_objects_range"],
                include_distractors=template["include_distractors"],
                seed=i * 1000
            )
            config.scene_name = f"{template['name']}_{i:03d}"
            all_configs.append(config)
    
    print(f"✓ 总共生成了 {len(all_configs)} 个配置")
    return all_configs
```

## 🤖 模型训练集成

### 示例5: 与SpatialVLA集成

```python
def spatialvla_training_integration():
    """与SpatialVLA训练集成示例"""
    
    # 模拟SpatialVLA训练循环
    env = simpler_env.make("custom_enhanced_medium_diversity")
    
    # 训练参数
    num_episodes = 1000
    max_steps = 100
    
    training_data = []
    
    for episode in range(num_episodes):
        # 重置环境
        obs, info = env.reset(seed=episode)
        
        # 获取语言指令
        instruction = env.get_language_instruction()
        
        episode_trajectory = []
        
        for step in range(max_steps):
            # 获取当前观察
            image = get_image_from_maniskill2_obs_dict(env, obs)
            
            # 这里应该调用SpatialVLA模型预测动作
            # action = model.predict(image, instruction)
            # 现在使用随机动作作为示例
            action = env.action_space.sample()
            
            # 执行动作
            next_obs, reward, terminated, truncated, step_info = env.step(action)
            
            # 记录轨迹数据
            step_data = {
                "step": step,
                "observation": image,  # 在实际使用中可能需要序列化
                "action": action.tolist(),
                "reward": reward,
                "instruction": instruction,
                "diversity_info": info.get("diversity_info", {})
            }
            episode_trajectory.append(step_data)
            
            obs = next_obs
            
            if terminated or truncated:
                break
        
        training_data.append({
            "episode": episode,
            "trajectory": episode_trajectory,
            "success": terminated,
            "scene_config": info.get("scene_config", {})
        })
        
        if episode % 100 == 0:
            print(f"完成 {episode} episodes")
    
    env.close()
    print(f"✓ 收集了 {len(training_data)} episodes的训练数据")
    return training_data
```

## 📊 多样性分析

### 示例6: 分析场景多样性

```python
import matplotlib.pyplot as plt
from collections import Counter

def analyze_scene_diversity(num_samples=200):
    """分析场景多样性"""
    
    env = simpler_env.make("custom_enhanced_high_diversity")
    
    diversity_stats = {
        "lighting_types": [],
        "object_counts": [],
        "material_variations": [],
        "physics_variations": []
    }
    
    for i in range(num_samples):
        obs, info = env.reset(seed=i)
        
        # 收集多样性信息
        diversity_info = info.get("diversity_info", {})
        
        # 光照类型
        if "lighting" in diversity_info:
            lighting_type = diversity_info["lighting"].get("type", "unknown")
            diversity_stats["lighting_types"].append(lighting_type)
        
        # 物体数量
        scene_config = info.get("scene_config", {})
        total_objects = scene_config.get("total_objects", 0)
        diversity_stats["object_counts"].append(total_objects)
        
        # 材质变化
        if "materials" in diversity_info:
            material_count = len(diversity_info["materials"])
            diversity_stats["material_variations"].append(material_count)
        
        # 物理变化
        if "physics" in diversity_info:
            gravity = diversity_info["physics"].get("gravity", 9.81)
            diversity_stats["physics_variations"].append(gravity)
    
    env.close()
    
    # 分析结果
    print("多样性分析结果:")
    print(f"样本数量: {num_samples}")
    
    # 光照类型分布
    lighting_counter = Counter(diversity_stats["lighting_types"])
    print(f"光照类型分布: {dict(lighting_counter)}")
    
    # 物体数量分布
    object_counter = Counter(diversity_stats["object_counts"])
    print(f"物体数量分布: {dict(object_counter)}")
    
    # 统计信息
    if diversity_stats["material_variations"]:
        avg_material_vars = np.mean(diversity_stats["material_variations"])
        print(f"平均材质变化数量: {avg_material_vars:.2f}")
    
    if diversity_stats["physics_variations"]:
        gravity_range = (min(diversity_stats["physics_variations"]), 
                        max(diversity_stats["physics_variations"]))
        print(f"重力变化范围: {gravity_range}")
    
    return diversity_stats
```

## 🔄 批量处理

### 示例7: 批量环境处理

```python
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor

def process_single_environment(args):
    """处理单个环境的函数"""
    env_name, episode_range, base_seed = args
    
    # 在子进程中导入
    import sys
    sys.path.append("SimplerEnv")
    import simpler_env
    
    env = simpler_env.make(env_name)
    results = []
    
    for episode in episode_range:
        obs, info = env.reset(seed=base_seed + episode)
        instruction = env.get_language_instruction()
        
        # 运行几步
        steps = 0
        while steps < 20:
            action = env.action_space.sample()
            obs, reward, terminated, truncated, step_info = env.step(action)
            steps += 1
            
            if terminated or truncated:
                break
        
        results.append({
            "episode": episode,
            "steps": steps,
            "success": terminated,
            "instruction": instruction,
            "scene_config": info.get("scene_config", {})
        })
    
    env.close()
    return env_name, results

def batch_environment_processing():
    """批量处理多个环境"""
    
    environments = [
        "custom_enhanced_low_diversity",
        "custom_enhanced_medium_diversity", 
        "custom_enhanced_high_diversity"
    ]
    
    episodes_per_env = 50
    base_seed = 1000
    
    # 准备参数
    args_list = []
    for env_name in environments:
        episode_range = range(episodes_per_env)
        args_list.append((env_name, episode_range, base_seed))
    
    # 并行处理
    with ProcessPoolExecutor(max_workers=3) as executor:
        results = list(executor.map(process_single_environment, args_list))
    
    # 整理结果
    all_results = {}
    for env_name, env_results in results:
        all_results[env_name] = env_results
        print(f"✓ {env_name}: 处理了 {len(env_results)} episodes")
    
    return all_results
```

## 🎮 交互式示例

### 示例8: 交互式场景探索

```python
def interactive_scene_exploration():
    """交互式场景探索"""
    
    print("交互式场景探索")
    print("可用环境:")
    environments = [
        "custom_diverse_pick_scene",
        "custom_enhanced_medium_diversity",
        "custom_enhanced_high_diversity"
    ]
    
    for i, env_name in enumerate(environments):
        print(f"{i + 1}. {env_name}")
    
    while True:
        try:
            choice = input("\n选择环境 (1-3) 或 'q' 退出: ")
            
            if choice.lower() == 'q':
                break
            
            env_idx = int(choice) - 1
            if 0 <= env_idx < len(environments):
                env_name = environments[env_idx]
                
                print(f"\n创建环境: {env_name}")
                env = simpler_env.make(env_name)
                
                # 重置环境
                seed = int(input("输入随机种子 (或按回车使用随机): ") or "0")
                obs, info = env.reset(seed=seed)
                
                print(f"语言指令: {env.get_language_instruction()}")
                print(f"场景配置: {info.get('scene_config', {})}")
                
                if "diversity_info" in info:
                    print(f"多样性信息: {list(info['diversity_info'].keys())}")
                
                env.close()
            else:
                print("无效选择")
                
        except (ValueError, KeyboardInterrupt):
            print("退出")
            break
```

## 🔧 实用工具函数

### 示例9: 配置验证和调试

```python
from custom_scene_config import validate_config, SceneConfigGenerator

def debug_configuration():
    """配置调试工具"""
    
    generator = SceneConfigGenerator()
    
    # 生成并验证配置
    for i in range(5):
        config = generator.generate_random_config(seed=i * 100)
        
        print(f"\n配置 {i + 1}:")
        print(f"场景名称: {config.scene_name}")
        print(f"目标物体: {len(config.target_objects)}")
        print(f"干扰物体: {len(config.distractor_objects)}")
        
        # 验证配置
        errors = validate_config(config)
        if errors:
            print(f"配置错误: {errors}")
        else:
            print("✓ 配置有效")

def environment_health_check():
    """环境健康检查"""
    
    environments = [
        "custom_diverse_pick_scene",
        "custom_enhanced_low_diversity",
        "custom_enhanced_medium_diversity"
    ]
    
    for env_name in environments:
        print(f"\n检查环境: {env_name}")
        
        try:
            env = simpler_env.make(env_name)
            obs, info = env.reset(seed=42)
            
            # 基本功能测试
            action = env.action_space.sample()
            obs, reward, terminated, truncated, step_info = env.step(action)
            
            print(f"✓ {env_name} 工作正常")
            env.close()
            
        except Exception as e:
            print(f"✗ {env_name} 出现问题: {e}")
```

---

这些示例展示了如何充分利用新的自定义多样化场景系统。根据具体需求，可以组合使用这些示例来构建完整的训练和评估流程。
