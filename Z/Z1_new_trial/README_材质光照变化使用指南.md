# SimplerEnv 材质和光照变化使用指南

## 🎯 概述

本指南介绍如何使用增强的 SimplerEnv 环境来实现明显的材质和光照变化效果，并提供了灵活的超参数接口。

## 📁 文件结构

```
Z/Z1_new_trial/
├── enhanced_diversity_demo.py          # 完整的多样性演示脚本
├── diversity_hyperparams.py            # 简化的超参数接口
├── diversity_enhancer.py               # 核心多样性增强器
├── simpler_env_demo.py                 # 基础演示脚本
└── SimplerEnv/                         # SimplerEnv 相关代码
    ├── ManiSkill2_real2sim/
    └── custom_scene_config.py
```

## 🚀 快速开始

### 方法1: 使用超参数接口（推荐）

```bash
# 1. 编辑超参数配置
nano diversity_hyperparams.py

# 2. 运行演示
python diversity_hyperparams.py
```

### 方法2: 使用完整演示脚本

```bash
# 演示单一配置
python enhanced_diversity_demo.py --material-intensity dramatic --lighting-intensity dramatic

# 演示所有强度组合
python enhanced_diversity_demo.py --demo-all

# 使用自定义参数
python enhanced_diversity_demo.py \
    --material-intensity medium \
    --lighting-intensity medium \
    --custom-color-intensity 3.0 \
    --custom-lighting-intensity 2.0
```

## 🎛️ 超参数配置

### 材质变化参数

在 `diversity_hyperparams.py` 中可以调整以下参数：

```python
MATERIAL_HYPERPARAMS = {
    # 摩擦力范围 [最小值, 最大值]
    "friction_range": [0.05, 5.0],
    
    # 弹性系数范围 [最小值, 最大值]  
    "restitution_range": [0.0, 0.95],
    
    # 密度范围 [最小值, 最大值]
    "density_range": [0.1, 5.0],
    
    # 金属度范围 [最小值, 最大值]
    "metallic_range": [0.0, 1.0],
    
    # 粗糙度范围 [最小值, 最大值]
    "roughness_range": [0.0, 1.0],
    
    # 自定义颜色变化 (RGB值，可以超过1.0)
    "color_variations": [
        (3.0, 0.2, 0.2),    # 超鲜红
        (0.2, 3.0, 0.2),    # 超鲜绿
        (0.2, 0.2, 3.0),    # 超鲜蓝
        # ... 更多颜色
    ]
}
```

### 光照变化参数

```python
LIGHTING_HYPERPARAMS = {
    # 环境光范围
    "ambient_range": [(0.0, 0.0, 0.0), (1.0, 1.0, 1.0)],
    
    # 方向光强度范围
    "directional_intensity_range": [0.1, 8.0],
    
    # 方向光颜色范围
    "directional_color_range": [(0.3, 0.3, 0.3), (3.0, 3.0, 3.0)],
    
    # 阴影概率
    "shadow_probability": 1.0,
    
    # 彩色光照概率
    "colored_light_probability": 0.6
}
```

## 🎨 变化强度级别

### 材质变化强度

- **subtle**: 微妙变化，接近原始外观
- **medium**: 中等变化，明显但不过分
- **dramatic**: 戏剧性变化，非常明显的效果

### 光照变化强度

- **subtle**: 轻微的光照调整
- **medium**: 中等强度的光照变化
- **dramatic**: 极端的光照效果

## 📊 输出文件

### 对比图像

生成的对比图像保存在：
```
/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/diversity_comparison/
```

文件命名格式：
```
diversity_comparison_{材质强度}_{光照强度}_{时间戳}.png
```

### 详细报告

JSON格式的详细报告保存在：
```
/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/diversity_reports/
```

## 🔧 高级用法

### 编程接口

```python
from diversity_enhancer import DiversityEnhancer

# 创建自定义增强器
enhancer = DiversityEnhancer(
    seed=42,
    material_intensity="dramatic",
    lighting_intensity="dramatic",
    custom_params={
        "material": {
            "friction_range": [0.1, 3.0],
            "color_variations": [(2.0, 0.2, 0.2), (0.2, 2.0, 0.2)]
        },
        "lighting": {
            "directional_intensity_range": [0.5, 5.0]
        }
    }
)

# 在环境中使用
env.diversity_enhancer = enhancer
```

### 命令行参数

```bash
# 自定义摩擦力范围
python enhanced_diversity_demo.py --custom-friction-range 0.1 2.0

# 自定义颜色强度
python enhanced_diversity_demo.py --custom-color-intensity 2.5

# 自定义光照强度
python enhanced_diversity_demo.py --custom-lighting-intensity 1.5
```

## 📈 效果展示

### 材质变化效果

1. **颜色变化**: 物体颜色从原始颜色变为鲜艳的红、绿、蓝等
2. **材质属性**: 金属度和粗糙度的变化影响反射效果
3. **物理属性**: 摩擦力和弹性系数影响物理行为

### 光照变化效果

1. **环境光**: 整体场景明暗度的变化
2. **方向光**: 强烈的定向光源创造阴影和高光
3. **彩色光照**: 使用彩色光源创造特殊氛围
4. **戏剧性光照**: 极端的明暗对比

## 🐛 故障排除

### 常见问题

1. **"a must be 1-dimensional" 错误**
   - 已修复，如果仍出现请检查numpy版本

2. **图像保存失败**
   - 检查输出目录权限
   - 确保有足够的磁盘空间

3. **材质变化不明显**
   - 增加 `custom_color_intensity` 参数
   - 使用 "dramatic" 强度级别

4. **光照变化不明显**
   - 增加 `directional_intensity_range`
   - 提高 `colored_light_probability`

### 调试技巧

```python
# 启用详细输出
env.verbose = True

# 检查多样性信息
print(env.current_diversity_info)

# 保存中间状态
cv2.imwrite("debug_image.png", image)
```

## 📝 最佳实践

1. **渐进式调整**: 从 subtle 开始，逐步增加强度
2. **参数组合**: 尝试不同的材质和光照强度组合
3. **批量测试**: 使用 `--demo-all` 生成所有组合的对比图
4. **自定义颜色**: 使用超过1.0的RGB值获得更鲜艳效果
5. **保存配置**: 记录有效的超参数配置以便复用

## 🔗 相关文件

- `材质变化错误修复报告.md`: 详细的错误修复过程
- `diversity_enhancer.py`: 核心实现代码
- `enhanced_diverse_env.py`: 增强的环境实现

## 💡 提示

- 颜色值可以超过1.0来获得超鲜艳效果
- 摩擦力和弹性系数会影响物理仿真行为
- 光照强度范围控制明暗对比度
- 使用不同的随机种子获得不同的变化效果
