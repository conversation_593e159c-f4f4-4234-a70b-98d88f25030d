#!/usr/bin/env python3
"""
创建包含10个物体的SimplerEnv场景案例

这个脚本专门用于生成包含10个物体的多样化场景图片。
"""

import os
import sys
import numpy as np
from pathlib import Path
from datetime import datetime

# 设置环境变量
os.environ['MUJOCO_GL'] = 'egl'
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 添加路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir / "SimplerEnv" / "ManiSkill2_real2sim"))
sys.path.insert(0, str(current_dir / "SimplerEnv"))

def create_10_objects_scene():
    """创建包含10个物体的场景"""
    print("🎯 创建包含10个物体的SimplerEnv场景")
    print("=" * 60)
    
    try:
        import gymnasium as gym
        import sapien.core as sapien
        from ManiSkill2_real2sim.mani_skill2_real2sim.envs.custom_scenes import enhanced_diverse_env
        from diversity_enhancer import DiversityEnhancer
        import cv2
        
        # 关闭降噪
        sapien.render_config.rt_use_denoiser = False
        
        print("✅ 模块导入成功")
        
        # 创建多样性增强器 - 戏剧性效果
        enhancer = DiversityEnhancer(
            seed=42,
            material_intensity="dramatic",
            lighting_intensity="medium",
            custom_params={
                "material": {
                    "friction_range": [0.2, 2.0],
                    "restitution_range": [0.0, 0.8],
                    "density_range": [200.0, 2000.0],
                    "metallic_range": [0.0, 1.0],
                    "roughness_range": [0.0, 1.0],
                    "color_variations": [
                        (2.0, 0.3, 0.3),    # 鲜红
                        (0.3, 2.0, 0.3),    # 鲜绿
                        (0.3, 0.3, 2.0),    # 鲜蓝
                        (2.0, 2.0, 0.3),    # 鲜黄
                        (2.0, 0.3, 2.0),    # 鲜紫
                        (0.3, 2.0, 2.0),    # 鲜青
                        (0.1, 0.1, 0.1),    # 深黑
                        (2.5, 2.5, 2.5),    # 亮白
                        (2.0, 1.0, 0.3),    # 橙色
                        (1.5, 0.3, 2.0),    # 深紫
                    ]
                },
                "lighting": {
                    "ambient_range": [(0.2, 0.2, 0.2), (0.6, 0.6, 0.6)],
                    "directional_intensity_range": [1.0, 4.0],
                    "directional_color_range": [(0.8, 0.8, 0.8), (1.5, 1.5, 1.5)],
                    "shadow_probability": 1.0,
                    "colored_light_probability": 0.3
                },
                "physics": {
                    "gravity_range": [9.0, 11.0],
                    "linear_damping_range": [0.1, 0.4],
                    "angular_damping_range": [0.1, 0.4],
                }
            }
        )
        
        # 创建环境配置 - 确保有10个物体
        env_config = {
            "robot": "google_robot_static",
            "scene_name": "dummy_tabletop",
            "obs_mode": "image",
            "camera_cfgs": {"add_segmentation": True},
            "render_mode": "cameras",
            "num_objects_range": (10, 10),  # 固定10个物体
            "diversity_level": "high",
            "lighting_variation_prob": 0.9,
            "material_variation_prob": 1.0,
            "physics_variation_prob": 0.8,
        }
        
        # 创建环境
        env = enhanced_diverse_env.EnhancedDiverseSceneEnv(**env_config)
        env.diversity_enhancer = enhancer
        
        print("✅ 环境创建成功")
        print(f"📦 配置物体数量: {env_config['num_objects_range']}")
        
        # 重置环境以应用变化
        obs, info = env.reset()
        print("✅ 环境重置成功，10个物体场景已生成")
        
        # 获取场景中的实际物体信息
        try:
            actors = env.get_actors()
            actor_names = [actor.name for actor in actors]
            print(f"🎯 场景中实际物体数量: {len(actor_names)}")
            print(f"   物体列表: {', '.join(actor_names)}")
        except Exception as e:
            print(f"⚠️ 无法获取物体列表: {e}")
        
        # 获取图像
        def get_image_from_obs(obs):
            if 'image' in obs and 'overhead_camera' in obs['image']:
                camera_obs = obs['image']['overhead_camera']
                if 'Color' in camera_obs:
                    return camera_obs['Color']
            return np.zeros((480, 640, 3), dtype=np.uint8)
        
        image = get_image_from_obs(obs)
        
        # 保存图像
        output_dir = Path("/home/<USER>/claude/SpatialVLA/Z/Z_new_trial")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        image_filename = f"10_objects_scene_dramatic_{timestamp}.png"
        image_path = output_dir / image_filename
        
        # 转换图像格式并保存
        if image.dtype == np.float32 or image.dtype == np.float64:
            image = (image * 255).astype(np.uint8)
        
        # 转换RGB到BGR用于OpenCV
        image_bgr = cv2.cvtColor(image[:,:,:3], cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(image_path), image_bgr)
        
        print(f"✅ 10个物体场景图像已保存: {image_path}")
        
        # 显示多样性信息
        if hasattr(env, 'current_diversity_info'):
            diversity_info = env.current_diversity_info
            print("\n📊 10个物体场景的多样性变化:")
            print("-" * 40)
            
            # 材质变化信息
            if 'materials' in diversity_info:
                materials = diversity_info['materials']
                print(f"🎨 材质变化物体数量: {len(materials)}")
                for i, mat_info in enumerate(materials[:5]):  # 只显示前5个
                    obj_name = mat_info.get('object_name', f'物体_{i+1}')
                    variations = mat_info.get('variations', [])
                    print(f"   📦 {obj_name}: {len(variations)} 种变化")
                if len(materials) > 5:
                    print(f"   ... 还有 {len(materials) - 5} 个物体")
            
            # 光照变化信息
            if 'lighting' in diversity_info:
                lighting = diversity_info['lighting']
                print(f"💡 光照变化: {lighting.get('type', '未知')}")
                if 'lights' in lighting:
                    print(f"   光源数量: {len(lighting['lights'])}")
        
        env.close()
        
        print(f"\n🎉 10个物体场景创建完成！")
        print(f"📁 图像文件: {image_path}")
        print("🎨 场景特点:")
        print("   - 包含10个不同类型的物体")
        print("   - 戏剧性材质变化效果")
        print("   - 中等光照变化")
        print("   - 物理属性多样化")
        
        return str(image_path)
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🎯 SimplerEnv 10个物体场景生成器")
    print("=" * 50)
    
    # 生成多个不同配置的10个物体场景
    scenes = []
    
    print("🚀 开始生成10个物体的场景...")
    
    # 生成场景
    scene_path = create_10_objects_scene()
    if scene_path:
        scenes.append(scene_path)
        print(f"\n✅ 场景生成成功!")
        print(f"📸 图像路径: {scene_path}")
    else:
        print("\n❌ 场景生成失败!")
    
    if scenes:
        print(f"\n🎉 总共生成了 {len(scenes)} 个10物体场景!")
        print("📁 所有图像文件:")
        for i, scene in enumerate(scenes, 1):
            print(f"   {i}. {scene}")
    
    return scenes

if __name__ == "__main__":
    main()
